import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Redis from 'ioredis';

/**
 * 速率限制服务
 * 使用Redis和令牌桶算法实现精细化的速率限制
 */
@Injectable()
export class RateLimiterService {
  private readonly logger = new Logger(RateLimiterService.name);
  private readonly redis: Redis;

  constructor(private readonly configService: ConfigService) {
    const redisConfig = this.configService.get('redis');
    this.redis = new Redis(redisConfig);
  }

  /**
   * 检查速率限制
   * @param key 限制键（通常是API密钥或用户ID）
   * @param limit 限制数量
   * @param windowMs 时间窗口（毫秒）
   * @param cost 本次请求消耗的配额（默认为1）
   */
  async checkRateLimit(
    key: string,
    limit: number,
    windowMs: number,
    cost: number = 1,
  ): Promise<{
    allowed: boolean;
    remaining: number;
    resetTime: number;
    totalHits: number;
  }> {
    const now = Date.now();
    const window = Math.floor(now / windowMs);
    const redisKey = `rate_limit:${key}:${window}`;

    try {
      // 使用Lua脚本确保原子性操作
      const luaScript = `
        local key = KEYS[1]
        local limit = tonumber(ARGV[1])
        local cost = tonumber(ARGV[2])
        local ttl = tonumber(ARGV[3])
        
        local current = redis.call('GET', key)
        if current == false then
          current = 0
        else
          current = tonumber(current)
        end
        
        if current + cost <= limit then
          local newValue = redis.call('INCRBY', key, cost)
          redis.call('EXPIRE', key, ttl)
          return {1, limit - newValue, newValue}
        else
          return {0, 0, current}
        end
      `;

      const result = (await this.redis.eval(
        luaScript,
        1,
        redisKey,
        limit.toString(),
        cost.toString(),
        Math.ceil(windowMs / 1000).toString(),
      )) as number[];

      const allowed = result[0] === 1;
      const remaining = result[1];
      const totalHits = result[2];
      const resetTime = (window + 1) * windowMs;

      if (!allowed) {
        this.logger.warn(`速率限制触发 - 键: ${key}, 当前: ${totalHits}, 限制: ${limit}`);
      }

      return {
        allowed,
        remaining,
        resetTime,
        totalHits,
      };
    } catch (error) {
      this.logger.error(`速率限制检查失败: ${error.message}`);
      // 在Redis错误时允许请求通过，避免服务完全不可用
      return {
        allowed: true,
        remaining: limit,
        resetTime: now + windowMs,
        totalHits: 0,
      };
    }
  }

  /**
   * 令牌桶算法实现
   * @param key 桶标识
   * @param capacity 桶容量
   * @param refillRate 每秒补充的令牌数
   * @param tokens 本次请求需要的令牌数
   */
  async checkTokenBucket(
    key: string,
    capacity: number,
    refillRate: number,
    tokens: number = 1,
  ): Promise<{
    allowed: boolean;
    remaining: number;
    retryAfter?: number;
  }> {
    const now = Date.now();
    const bucketKey = `token_bucket:${key}`;

    try {
      const luaScript = `
        local key = KEYS[1]
        local capacity = tonumber(ARGV[1])
        local refillRate = tonumber(ARGV[2])
        local tokens = tonumber(ARGV[3])
        local now = tonumber(ARGV[4])
        
        local bucket = redis.call('HMGET', key, 'tokens', 'lastRefill')
        local currentTokens = tonumber(bucket[1]) or capacity
        local lastRefill = tonumber(bucket[2]) or now
        
        -- 计算需要补充的令牌
        local timePassed = (now - lastRefill) / 1000
        local tokensToAdd = math.floor(timePassed * refillRate)
        currentTokens = math.min(capacity, currentTokens + tokensToAdd)
        
        if currentTokens >= tokens then
          currentTokens = currentTokens - tokens
          redis.call('HMSET', key, 'tokens', currentTokens, 'lastRefill', now)
          redis.call('EXPIRE', key, 3600)
          return {1, currentTokens, 0}
        else
          redis.call('HMSET', key, 'tokens', currentTokens, 'lastRefill', now)
          redis.call('EXPIRE', key, 3600)
          local retryAfter = math.ceil((tokens - currentTokens) / refillRate)
          return {0, currentTokens, retryAfter}
        end
      `;

      const result = (await this.redis.eval(
        luaScript,
        1,
        bucketKey,
        capacity.toString(),
        refillRate.toString(),
        tokens.toString(),
        now.toString(),
      )) as number[];

      const allowed = result[0] === 1;
      const remaining = result[1];
      const retryAfter = result[2] > 0 ? result[2] : undefined;

      if (!allowed) {
        this.logger.warn(
          `令牌桶限制触发 - 键: ${key}, 剩余令牌: ${remaining}, 重试时间: ${retryAfter}s`,
        );
      }

      return {
        allowed,
        remaining,
        retryAfter,
      };
    } catch (error) {
      this.logger.error(`令牌桶检查失败: ${error.message}`);
      return {
        allowed: true,
        remaining: capacity,
      };
    }
  }

  /**
   * 检查并应用速率限制
   * @param apiKeyId API密钥ID
   * @param rateLimit 每分钟请求限制
   * @param estimatedTokens 预估token消耗
   */
  async applyRateLimit(
    apiKeyId: string,
    rateLimit: number,
    estimatedTokens: number = 0,
  ): Promise<void> {
    // 检查每分钟请求限制
    const requestLimit = await this.checkRateLimit(
      `requests:${apiKeyId}`,
      rateLimit,
      60 * 1000, // 1分钟窗口
    );

    if (!requestLimit.allowed) {
      throw new HttpException(
        {
          error: 'Rate limit exceeded',
          message: '请求频率超过限制',
          retryAfter: Math.ceil((requestLimit.resetTime - Date.now()) / 1000),
          limit: rateLimit,
          remaining: requestLimit.remaining,
        },
        HttpStatus.TOO_MANY_REQUESTS,
      );
    }

    // 如果有token估算，检查token速率限制
    if (estimatedTokens > 0) {
      const tokenLimit = await this.checkTokenBucket(
        `tokens:${apiKeyId}`,
        10000, // 令牌桶容量
        100, // 每秒补充100个令牌
        estimatedTokens,
      );

      if (!tokenLimit.allowed) {
        throw new HttpException(
          {
            error: 'Token rate limit exceeded',
            message: 'Token消耗速率超过限制',
            retryAfter: tokenLimit.retryAfter,
            remaining: tokenLimit.remaining,
          },
          HttpStatus.TOO_MANY_REQUESTS,
        );
      }
    }
  }

  /**
   * 获取速率限制状态
   */
  async getRateLimitStatus(apiKeyId: string): Promise<{
    requests: {
      limit: number;
      remaining: number;
      resetTime: number;
    };
    tokens: {
      remaining: number;
      capacity: number;
    };
  }> {
    const now = Date.now();
    const window = Math.floor(now / (60 * 1000));
    const requestKey = `rate_limit:requests:${apiKeyId}:${window}`;
    const tokenKey = `token_bucket:tokens:${apiKeyId}`;

    try {
      const [requestCount, tokenBucket] = await Promise.all([
        this.redis.get(requestKey),
        this.redis.hmget(tokenKey, 'tokens', 'lastRefill'),
      ]);

      const currentRequests = parseInt(requestCount || '0');
      const currentTokens = parseInt(tokenBucket[0] || '10000');

      return {
        requests: {
          limit: 1000, // 默认限制，实际应该从配置获取
          remaining: Math.max(0, 1000 - currentRequests),
          resetTime: (window + 1) * 60 * 1000,
        },
        tokens: {
          remaining: currentTokens,
          capacity: 10000,
        },
      };
    } catch (error) {
      this.logger.error(`获取速率限制状态失败: ${error.message}`);
      return {
        requests: {
          limit: 1000,
          remaining: 1000,
          resetTime: now + 60 * 1000,
        },
        tokens: {
          remaining: 10000,
          capacity: 10000,
        },
      };
    }
  }

  /**
   * 重置速率限制
   */
  async resetRateLimit(apiKeyId: string): Promise<void> {
    const pattern = `*:${apiKeyId}*`;
    const keys = await this.redis.keys(pattern);

    if (keys.length > 0) {
      await this.redis.del(...keys);
      this.logger.log(`已重置API密钥 ${apiKeyId} 的速率限制`);
    }
  }

  /**
   * 清理过期的速率限制数据
   */
  // eslint-disable-next-line
  async cleanupExpiredData(): Promise<void> {
    try {
      // Redis的EXPIRE会自动清理过期数据，这里可以添加额外的清理逻辑
      this.logger.debug('执行速率限制数据清理');
    } catch (error) {
      this.logger.error(`清理过期数据失败: ${error.message}`);
    }
  }
}
