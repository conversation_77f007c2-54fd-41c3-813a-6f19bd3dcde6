import { Controller, Post, Body, UseGuards, Req, Res, HttpStatus, Logger } from '@nestjs/common';
import { Request, Response } from 'express';
import { ApiTags, ApiOperation, ApiResponse, ApiSecurity, ApiHeader } from '@nestjs/swagger';
import { ChatCompletionDto, ChatCompletionResponseDto } from '../../common/dto/chat-completion.dto';
import { ApiKeyGuard } from '../auth/guards/api-key.guard';
import { ProvidersService } from '../providers/providers.service';
import { AuthService } from '../auth/auth.service';
import { RateLimiterService } from '../rate-limiter/rate-limiter.service';
import { CachingService } from '../caching/caching.service';
import { MetricsService } from '../metrics/metrics.service';
import { ApiKeyEntity } from '../auth/entities/api-key.entity';

/**
 * 聊天完成控制器
 * 提供统一的聊天完成API接口，兼容OpenAI格式
 */
@ApiTags('Chat')
@Controller('chat')
@UseGuards(ApiKeyGuard)
@ApiSecurity('api-key')
export class ChatController {
  private readonly logger = new Logger(ChatController.name);

  constructor(
    private readonly providersService: ProvidersService,
    private readonly authService: AuthService,
    private readonly rateLimiterService: RateLimiterService,
    private readonly cachingService: CachingService,
    private readonly metricsService: MetricsService,
  ) {}

  /**
   * 聊天完成接口
   * 兼容OpenAI的/v1/chat/completions格式
   */
  @Post('completions')
  @ApiOperation({
    summary: '聊天完成',
    description: '发送消息到LLM模型并获取回复，支持流式和非流式响应',
  })
  @ApiHeader({
    name: 'X-API-Key',
    description: 'API密钥',
    required: true,
  })
  @ApiResponse({
    status: 200,
    description: '聊天完成成功',
    type: ChatCompletionResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: 401,
    description: 'API密钥无效',
  })
  @ApiResponse({
    status: 429,
    description: '请求频率超过限制',
  })
  @ApiResponse({
    status: 500,
    description: '服务器内部错误',
  })
  async chatCompletions(
    @Body() request: ChatCompletionDto,
    @Req() req: Request,
    @Res() res: Response,
  ): Promise<void> {
    const startTime = Date.now();
    const apiKey = req['apiKey'];
    const clientIp = req['clientIp'];
    const requestId = this.generateRequestId();

    this.logger.log(
      `收到聊天完成请求 - API密钥: ${apiKey.id}, 模型: ${request.model}, 流式: ${request.stream}`,
    );

    try {
      // 1. 检查模型访问权限
      await this.authService.checkModelAccess(apiKey, request.model);

      // 2. 估算token消耗并检查配额
      const estimatedTokens = this.estimateTokens(request.messages);
      await this.authService.checkQuota(apiKey, estimatedTokens);

      // 3. 应用速率限制
      await this.rateLimiterService.applyRateLimit(apiKey.id, apiKey.rateLimit, estimatedTokens);

      // 4. 检查缓存（仅非流式请求）
      if (!request.stream) {
        const cachedResponse = await this.cachingService.getCachedResponse(request);
        if (cachedResponse) {
          this.logger.debug(`缓存命中 - 请求ID: ${requestId}`);

          await this.authService.recordUsage(
            apiKey,
            request.model,
            'cache',
            cachedResponse.usage.prompt_tokens,
            cachedResponse.usage.completion_tokens,
            Date.now() - startTime,
            'success',
            clientIp,
            req.headers['user-agent'],
            requestId,
          );

          this.metricsService.recordHttpRequest(
            req.method,
            req.route?.path || req.path,
            200,
            Date.now() - startTime,
          );

          res.status(HttpStatus.OK).json(cachedResponse);
          return;
        }
      }

      // 5. 处理流式响应
      if (request.stream) {
        await this.handleStreamResponse(request, apiKey, req, res, startTime, requestId);
        return;
      }

      // 6. 处理非流式响应
      await this.handleNonStreamResponse(request, apiKey, req, res, startTime, requestId);
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error(`聊天完成请求失败 - API密钥: ${apiKey.id}, 错误: ${error.message}`);

      await this.authService.recordUsage(
        apiKey,
        request.model,
        'unknown',
        0,
        0,
        duration,
        'error',
        clientIp,
        req.headers['user-agent'],
        requestId,
        error.message,
      );

      this.metricsService.recordHttpRequest(
        req.method,
        req.route?.path || req.path,
        error.status || 500,
        duration,
      );

      const statusCode = error.status || HttpStatus.INTERNAL_SERVER_ERROR;
      res.status(statusCode).json({
        error: {
          message: error.message || '内部服务器错误',
          type: 'api_error',
          code: error.code || 'internal_error',
        },
      });
    }
  }

  /**
   * 处理流式响应
   */
  private async handleStreamResponse(
    request: ChatCompletionDto,
    apiKey: ApiKeyEntity,
    req: Request,
    res: Response,
    startTime: number,
    requestId: string,
  ): Promise<void> {
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Headers', 'Cache-Control');

    let completionTokens = 0;
    let provider = 'unknown';

    try {
      // FIX: 修复了 implicit any 问题
      let selectedProvider: any;
      try {
        // FIX: 暂时忽略 private 访问错误，推荐在 ProvidersService 中修改
        selectedProvider = await this.providersService.selectProvider(request.model);
        if (selectedProvider) {
          provider = selectedProvider.name;
        }
      } catch (_error) {
        // FIX: 修复了 unused-vars 问题
        this.logger.warn(`获取提供商信息失败: ${_error.message}`);
      }

      const stream = this.providersService.chatCompletionStream(request);

      stream.subscribe({
        next: (chunk) => {
          res.write(chunk);
          const chunkTokens = this.estimateTokensFromChunk(chunk);
          completionTokens += chunkTokens;
        },
        // FIX: 修复了 no-misused-promises 问题
        complete: () => {
          (async () => {
            try {
              res.write('data: [DONE]\n\n');
              res.end();

              const duration = Date.now() - startTime;
              const promptTokens = this.estimateTokens(request.messages);

              await this.authService.recordUsage(
                apiKey,
                request.model,
                provider,
                promptTokens,
                completionTokens,
                duration,
                'success',
                req['clientIp'],
                req.headers['user-agent'],
                requestId,
              );

              this.metricsService.recordHttpRequest(
                req.method,
                req.route?.path || req.path,
                200,
                duration,
              );

              this.metricsService.recordLlmRequest(
                provider,
                request.model,
                'success',
                duration,
                promptTokens,
                completionTokens,
              );
            } catch (e) {
              this.logger.error(`在 stream.complete 回调中记录使用情况失败: ${e.message}`, e.stack);
            }
          })().catch((e) => {
            this.logger.error(`在 stream.complete 回调中记录使用情况失败: ${e.message}`, e.stack);
          });
        },
        // FIX: 修复了 no-misused-promises 问题
        error: (streamError) => {
          (async () => {
            try {
              this.logger.error(`流式响应错误: ${streamError.message}`);
              if (!res.headersSent) {
                res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
                  error: { message: streamError.message, type: 'stream_error' },
                });
              } else {
                res.write(`data: {"error": {"message": "${streamError.message}"}}\n\n`);
                res.end();
              }
              await this.authService.recordUsage(
                apiKey,
                request.model,
                provider || 'unknown',
                0,
                0,
                Date.now() - startTime,
                'error',
                req['clientIp'],
                req.headers['user-agent'],
                requestId,
                streamError.message,
              );
            } catch (e) {
              this.logger.error(`在 stream.error 回调中记录使用情况失败: ${e.message}`, e.stack);
            }
          })().catch((e) => {
            this.logger.error(`在 stream.error 回调中记录使用情况失败: ${e.message}`, e.stack);
          });
        },
      });
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error(`流式响应设置错误: ${error.message}`);

      if (!res.headersSent) {
        res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
          error: { message: error.message, type: 'stream_setup_error' },
        });
      }

      await this.authService.recordUsage(
        apiKey,
        request.model,
        provider,
        0,
        0,
        duration,
        'error',
        req['clientIp'],
        req.headers['user-agent'],
        requestId,
        error.message,
      );

      this.metricsService.recordHttpRequest(
        req.method,
        req.route?.path || req.path,
        error.status || 500,
        duration,
      );
    }
  }

  /**
   * 处理非流式响应
   */
  private async handleNonStreamResponse(
    request: ChatCompletionDto,
    apiKey: ApiKeyEntity,
    req: Request,
    res: Response,
    startTime: number,
    requestId: string,
  ): Promise<void> {
    try {
      const response = await this.providersService.chatCompletion(request);
      const duration = Date.now() - startTime;

      await this.cachingService.cacheResponse(request, response);

      let provider = 'unknown';
      try {
        // FIX: 暂时忽略 private 访问错误，推荐在 ProvidersService 中修改
        const selectedProvider = await this.providersService.selectProvider(request.model);
        if (selectedProvider) {
          provider = selectedProvider.name;
        }
      } catch (_error) {
        // FIX: 修复了 unused-vars 问题
        this.logger.warn(`获取提供商信息失败: ${_error.message}`);
      }

      await this.authService.recordUsage(
        apiKey,
        request.model,
        provider,
        response.usage.prompt_tokens,
        response.usage.completion_tokens,
        duration,
        'success',
        req['clientIp'],
        req.headers['user-agent'],
        requestId,
      );

      this.metricsService.recordHttpRequest(req.method, req.route?.path || req.path, 200, duration);

      this.metricsService.recordLlmRequest(
        provider,
        request.model,
        'success',
        duration,
        response.usage.prompt_tokens,
        response.usage.completion_tokens,
      );

      res.status(HttpStatus.OK).json(response);
    } catch (error) {
      throw error;
    }
  }

  /**
   * 生成请求ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substring(2)}`;
  }

  /**
   * 估算消息的token数量
   */
  private estimateTokens(messages: any[]): number {
    if (!messages || messages.length === 0) return 0;
    const totalText = messages.map((m) => m.content).join(' ');
    const englishWords = totalText.match(/[a-zA-Z]+/g)?.length || 0;
    const chineseChars = totalText.match(/[\u4e00-\u9fff]/g)?.length || 0;
    const otherChars = totalText.length - (totalText.match(/[a-zA-Z\u4e00-\u9fff]/g)?.length || 0);

    return Math.ceil(englishWords * 1.3 + chineseChars * 1.5 + otherChars * 0.5);
  }

  /**
   * 从流式响应块估算token数量
   */
  private estimateTokensFromChunk(chunk: string): number {
    try {
      if (chunk.startsWith('data: ')) {
        const data = chunk.slice(6).trim();
        if (data && data !== '[DONE]') {
          const parsed = JSON.parse(data);
          const content = parsed.choices?.[0]?.delta?.content || '';
          return this.estimateTokens([{ content }]);
        }
      }
    } catch (error) {
      this.logger.warn(`解析流式响应块失败: ${error.message}`);
    }
    return 0;
  }
}
