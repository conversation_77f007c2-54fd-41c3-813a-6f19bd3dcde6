import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TerminusModule } from '@nestjs/terminus';
import { ConfigService } from '@nestjs/config';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { ConfigModule } from '../../config/config.module';
import { AuthModule } from '../auth/auth.module';
import { ProvidersModule } from '../providers/providers.module';
import { RateLimiterModule } from '../rate-limiter/rate-limiter.module';
import { CachingModule } from '../caching/caching.module';
import { MetricsModule } from '../metrics/metrics.module';
import { ChatController } from './chat.controller';
import { HealthController } from './health.controller';
import { AppService } from './app.service';
import { ResponseInterceptor } from '../../common/interceptors/response.interceptor';

/**
 * 主应用模块
 * 整合所有功能模块，配置全局拦截器和中间件
 */
@Module({
  imports: [
    // 配置模块
    ConfigModule,
    
    // 数据库模块
    TypeOrmModule.forRootAsync({
      useFactory: (configService: ConfigService) => configService.get('database'),
      inject: [ConfigService],
    }),
    
    // 健康检查模块
    TerminusModule,
    
    // 业务模块
    AuthModule,
    ProvidersModule,
    RateLimiterModule,
    CachingModule,
    MetricsModule,
  ],
  controllers: [ChatController, HealthController],
  providers: [
    AppService,
    // 全局响应拦截器
    {
      provide: APP_INTERCEPTOR,
      useClass: ResponseInterceptor,
    },
  ],
})
export class AppModule {}