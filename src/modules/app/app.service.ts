import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ProvidersService } from '../providers/providers.service';
import { MetricsService } from '../metrics/metrics.service';

/**
 * 应用信息接口
 */
interface AppInfo {
  name: string;
  version: string;
  description: string;
  environment: string | undefined;
  uptime: number;
  timestamp: string;
}

/**
 * 应用状态接口
 */
interface AppStatus {
  status: string;
  providers: ProviderStatus;
  metrics: {
    enabled: boolean;
  };
  system: {
    nodeVersion: string;
    platform: string;
    arch: string;
    memory: NodeJS.MemoryUsage;
    uptime: number;
  };
}

/**
 * 模型信息接口
 */
export interface ModelInfo {
  id: string;
  providers: string[];
  created: number;
  owned_by: string;
}

/**
 * 提供者状态接口
 */
interface ProviderStatus {
  [provider: string]: {
    name: string;
    healthy: boolean;
    connections: number;
    supportedModels: string[];
    weight: number;
  };
}

/**
 * 模型列表响应接口
 */
export interface ModelsResponse {
  object: string;
  data: ModelInfo[];
}

/**
 * 主应用服务
 * 提供应用级别的通用功能和状态管理
 */
@Injectable()
export class AppService {
  // 使用 logger 记录关键操作
  private readonly logger = new Logger(AppService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly providersService: ProvidersService,
    private readonly metricsService: MetricsService,
  ) {}

  /**
   * 获取应用信息
   */
  getAppInfo(): AppInfo {
    this.logger.debug('获取应用信息');
    return {
      name: 'LLM API Gateway',
      version: '1.0.0',
      description: '高性能多模型LLM API网关',
      environment: this.configService.get<string>('app.nodeEnv'),
      uptime: process.uptime(),
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * 获取应用状态
   */
  async getAppStatus(): Promise<AppStatus> {
    this.logger.debug('获取应用状态');
    const providersStatus = await this.providersService.getProvidersStatus() as ProviderStatus;
    // 如果healthCheck不是异步方法，不要使用await
    const metricsEnabled = this.metricsService.healthCheck();
    
    return {
      status: 'running',
      providers: providersStatus,
      metrics: {
        enabled: metricsEnabled,
      },
      system: {
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch,
        memory: process.memoryUsage(),
        uptime: process.uptime(),
      },
    };
  }

  /**
   * 获取支持的模型列表
   * @returns 模型列表响应
   */
  getSupportedModels(): ModelsResponse {
    this.logger.debug('获取支持的模型列表');
    const rawModelMapping = this.providersService.getModelMapping();
    
    // 创建一个类型安全的模型映射
    const modelMapping: Record<string, string[]> = {};
    Object.entries(rawModelMapping).forEach(([model, providers]) => {
      if (Array.isArray(providers)) {
        modelMapping[model] = providers;
      } else {
        modelMapping[model] = [];
      }
    });

    const models: ModelInfo[] = Object.keys(modelMapping).map((model) => ({
      id: model,
      providers: modelMapping[model],
      created: Math.floor(Date.now() / 1000),
      owned_by: 'llm-gateway',
    }));

    return {
      object: 'list',
      data: models,
    };
  }

  // 注释掉未使用的方法，或者在实际使用时取消注释
  /*
  // 以下方法由生命周期钩子调用，保留但标记为内部方法

  /**
   * 初始化应用
   * @internal 由应用启动钩子调用
   */
  /*
  async initialize(): Promise<void> {
    this.logger.log('正在初始化LLM API网关...');
    
    try {
      // 刷新模型映射
      await this.providersService.refreshModelMapping();
      
      this.logger.log('LLM API网关初始化完成');
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      this.logger.error(`初始化失败: ${errorMessage}`);
      throw error;
    }
  }

  /**
   * 优雅关闭
   * @internal 由应用关闭钩子调用
   */
  /*
  async shutdown(): Promise<void> {
    this.logger.log('正在关闭LLM API网关...');
    
    try {
      // 这里可以添加清理逻辑
      // 例如关闭连接、保存状态等
      
      // 执行一些异步清理操作
      const cleanupTasks = [
        // 可以添加实际的清理任务
        Promise.resolve()
      ];
      
      await Promise.all(cleanupTasks);
      
      this.logger.log('LLM API网关已安全关闭');
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      this.logger.error(`关闭时发生错误: ${errorMessage}`);
    }
  }
  */
}
