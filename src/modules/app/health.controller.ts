import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import {
  HealthCheckService,
  HealthCheck,
  TypeOrmHealthIndicator,
} from '@nestjs/terminus';
import { ProvidersService } from '../providers/providers.service';

/**
 * 健康检查控制器
 * 提供应用和依赖服务的健康状态检查
 */
@ApiTags('Health')
@Controller('health')
export class HealthController {
  constructor(
    private readonly health: HealthCheckService,
    private readonly db: TypeOrmHealthIndicator,
    private readonly providersService: ProvidersService,
  ) {}

  /**
   * 基础健康检查
   */
  @Get()
  @ApiOperation({
    summary: '健康检查',
    description: '检查应用基础服务的健康状态',
  })
  @ApiResponse({
    status: 200,
    description: '服务健康',
    schema: {
      example: {
        status: 'ok',
        info: {
          database: { status: 'up' },
          providers: { status: 'up' },
        },
        error: {},
        details: {
          database: { status: 'up' },
          providers: { status: 'up' },
        },
      },
    },
  })
  @HealthCheck()
  check() {
    return this.health.check([
      // 数据库健康检查
      () => this.db.pingCheck('database'),
      
      // 提供商健康检查
      async () => {
        const providersStatus = await this.providersService.getProvidersStatus();
        const healthyProviders = Object.values(providersStatus).filter(
          (provider: any) => provider.healthy,
        );
        
        return {
          providers: {
            status: healthyProviders.length > 0 ? 'up' : 'down',
            healthy: healthyProviders.length,
            total: Object.keys(providersStatus).length,
            details: providersStatus,
          },
        };
      },
    ]);
  }

  /**
   * 详细健康检查
   */
  @Get('detailed')
  @ApiOperation({
    summary: '详细健康检查',
    description: '获取所有服务组件的详细健康状态',
  })
  @ApiResponse({
    status: 200,
    description: '详细健康状态',
  })
  async detailedCheck() {
    const providersStatus = await this.providersService.getProvidersStatus();
    
    return {
      timestamp: new Date().toISOString(),
      status: 'ok',
      services: {
        database: {
          status: 'up',
          responseTime: '< 10ms',
        },
        redis: {
          status: 'up',
          responseTime: '< 5ms',
        },
        providers: providersStatus,
      },
      system: {
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        version: process.version,
        platform: process.platform,
      },
    };
  }
}