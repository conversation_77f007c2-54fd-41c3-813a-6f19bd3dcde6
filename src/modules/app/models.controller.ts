import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { AppService, ModelsResponse } from './app.service';

/**
 * 模型列表控制器
 * 提供兼容OpenAI的模型列表接口
 */
@ApiTags('Models')
@Controller('models')
export class ModelsController {
  constructor(private readonly appService: AppService) {}

  /**
   * 获取支持的模型列表
   * 兼容OpenAI的/v1/models接口
   */
  @Get()
  @ApiOperation({
    summary: '获取模型列表',
    description: '返回网关支持的所有LLM模型列表，兼容OpenAI格式',
  })
  @ApiResponse({
    status: 200,
    description: '模型列表',
    schema: {
      example: {
        object: 'list',
        data: [
          {
            id: 'gpt-3.5-turbo',
            providers: ['openai'],
            created: **********,
            owned_by: 'llm-gateway',
          },
          {
            id: 'claude-3-sonnet-20240229',
            providers: ['anthropic'],
            created: **********,
            owned_by: 'llm-gateway',
          },
        ],
      },
    },
  })
  getModels(): ModelsResponse {
    return this.appService.getSupportedModels();
  }
}