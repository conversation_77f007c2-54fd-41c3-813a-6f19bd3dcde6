import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Redis from 'ioredis';
import * as crypto from 'crypto';
import { ChatCompletionDto, ChatCompletionResponseDto } from '../../common/dto/chat-completion.dto';

/**
 * 缓存服务
 * 使用Redis缓存聊天完成响应，提高性能并减少API调用成本
 */
@Injectable()
export class CachingService {
  private readonly logger = new Logger(CachingService.name);
  private readonly redis: Redis;
  private readonly defaultTtl: number;
  private readonly enabled: boolean;

  constructor(private readonly configService: ConfigService) {
    const redisConfig = this.configService.get('redis');
    const cacheConfig = this.configService.get('app.cache');

    this.redis = new Redis(redisConfig);
    this.defaultTtl = cacheConfig.ttl;
    this.enabled = cacheConfig.enabled;
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(request: ChatCompletionDto): string {
    // 创建请求的标准化表示
    const normalizedRequest = {
      model: request.model,
      messages: request.messages,
      temperature: request.temperature || 1,
      max_tokens: request.max_tokens,
      top_p: request.top_p || 1,
      frequency_penalty: request.frequency_penalty || 0,
      presence_penalty: request.presence_penalty || 0,
      stop: request.stop,
    };

    // 生成哈希
    const hash = crypto
      .createHash('sha256')
      .update(JSON.stringify(normalizedRequest))
      .digest('hex');

    return `chat_completion:${hash}`;
  }

  /**
   * 获取缓存的响应
   */
  async getCachedResponse(request: ChatCompletionDto): Promise<ChatCompletionResponseDto | null> {
    if (!this.enabled || request.stream) {
      return null;
    }

    try {
      const cacheKey = this.generateCacheKey(request);
      const cached = await this.redis.get(cacheKey);

      if (cached) {
        this.logger.debug(`缓存命中: ${cacheKey}`);
        return JSON.parse(cached);
      }

      this.logger.debug(`缓存未命中: ${cacheKey}`);
      return null;
    } catch (error) {
      this.logger.error(`获取缓存失败: ${error.message}`);
      return null;
    }
  }

  /**
   * 缓存响应
   */
  async cacheResponse(
    request: ChatCompletionDto,
    response: ChatCompletionResponseDto,
    ttl?: number,
  ): Promise<void> {
    if (!this.enabled || request.stream) {
      return;
    }

    try {
      const cacheKey = this.generateCacheKey(request);
      const cacheTtl = ttl || this.defaultTtl;

      await this.redis.setex(cacheKey, cacheTtl, JSON.stringify(response));

      this.logger.debug(`已缓存响应: ${cacheKey}, TTL: ${cacheTtl}s`);
    } catch (error) {
      this.logger.error(`缓存响应失败: ${error.message}`);
    }
  }

  /**
   * 删除缓存
   */
  async invalidateCache(pattern: string): Promise<number> {
    try {
      const keys = await this.redis.keys(`chat_completion:${pattern}*`);

      if (keys.length > 0) {
        const deleted = await this.redis.del(...keys);
        this.logger.log(`已删除 ${deleted} 个缓存项，模式: ${pattern}`);
        return deleted;
      }

      return 0;
    } catch (error) {
      this.logger.error(`删除缓存失败: ${error.message}`);
      return 0;
    }
  }

  /**
   * 清空所有缓存
   */
  async clearAllCache(): Promise<void> {
    try {
      const keys = await this.redis.keys('chat_completion:*');

      if (keys.length > 0) {
        await this.redis.del(...keys);
        this.logger.log(`已清空所有缓存，共 ${keys.length} 项`);
      }
    } catch (error) {
      this.logger.error(`清空缓存失败: ${error.message}`);
    }
  }

  /**
   * 获取缓存统计信息
   */
  async getCacheStats(): Promise<{
    totalKeys: number;
    memoryUsage: string;
    hitRate?: number;
  }> {
    try {
      const keys = await this.redis.keys('chat_completion:*');
      const info = await this.redis.info('memory');

      // 解析内存使用信息
      const memoryMatch = info.match(/used_memory_human:(.+)/);
      const memoryUsage = memoryMatch ? memoryMatch[1].trim() : 'Unknown';

      return {
        totalKeys: keys.length,
        memoryUsage,
      };
    } catch (error) {
      this.logger.error(`获取缓存统计失败: ${error.message}`);
      return {
        totalKeys: 0,
        memoryUsage: 'Unknown',
      };
    }
  }

  /**
   * 预热缓存
   */
  async warmupCache(commonRequests: ChatCompletionDto[]): Promise<void> {
    this.logger.log(`开始预热缓存，共 ${commonRequests.length} 个请求`);

    for (const request of commonRequests) {
      try {
        const cacheKey = this.generateCacheKey(request);
        const exists = await this.redis.exists(cacheKey);

        if (!exists) {
          // 这里可以调用实际的LLM服务来生成响应并缓存
          this.logger.debug(`预热缓存项: ${cacheKey}`);
        }
      } catch (error) {
        this.logger.error(`预热缓存失败: ${error.message}`);
      }
    }
  }

  /**
   * 设置缓存过期时间
   */
  async setCacheExpiry(pattern: string, ttl: number): Promise<number> {
    try {
      const keys = await this.redis.keys(`chat_completion:${pattern}*`);
      let updated = 0;

      for (const key of keys) {
        await this.redis.expire(key, ttl);
        updated++;
      }

      this.logger.log(`已更新 ${updated} 个缓存项的过期时间为 ${ttl}s`);
      return updated;
    } catch (error) {
      this.logger.error(`设置缓存过期时间失败: ${error.message}`);
      return 0;
    }
  }

  /**
   * 获取缓存项详情
   */
  async getCacheDetails(cacheKey: string): Promise<{
    exists: boolean;
    ttl: number;
    size: number;
    content?: any;
  }> {
    try {
      const fullKey = cacheKey.startsWith('chat_completion:')
        ? cacheKey
        : `chat_completion:${cacheKey}`;

      const [exists, ttl, content] = await Promise.all([
        this.redis.exists(fullKey),
        this.redis.ttl(fullKey),
        this.redis.get(fullKey),
      ]);

      return {
        exists: exists === 1,
        ttl,
        size: content ? Buffer.byteLength(content, 'utf8') : 0,
        content: content ? JSON.parse(content) : undefined,
      };
    } catch (error) {
      this.logger.error(`获取缓存详情失败: ${error.message}`);
      return {
        exists: false,
        ttl: -1,
        size: 0,
      };
    }
  }

  /**
   * 批量获取缓存
   */
  async batchGetCache(
    requests: ChatCompletionDto[],
  ): Promise<Map<string, ChatCompletionResponseDto>> {
    const results = new Map<string, ChatCompletionResponseDto>();

    if (!this.enabled) {
      return results;
    }

    try {
      const keys = requests.map((req) => this.generateCacheKey(req));
      const values = await this.redis.mget(...keys);

      for (let i = 0; i < keys.length; i++) {
        if (values[i]) {
          try {
            results.set(keys[i], JSON.parse(values[i]));
          } catch (error) {
            this.logger.warn(`解析缓存数据失败: ${keys[i]}`);
            this.logger.warn(error.message);
          }
        }
      }

      this.logger.debug(`批量获取缓存，命中 ${results.size}/${requests.length} 项`);
    } catch (error) {
      this.logger.error(`批量获取缓存失败: ${error.message}`);
    }

    return results;
  }

  /**
   * 批量设置缓存
   */
  async batchSetCache(
    items: Array<{
      request: ChatCompletionDto;
      response: ChatCompletionResponseDto;
      ttl?: number;
    }>,
  ): Promise<void> {
    if (!this.enabled || items.length === 0) {
      return;
    }

    try {
      const pipeline = this.redis.pipeline();

      for (const item of items) {
        if (!item.request.stream) {
          const cacheKey = this.generateCacheKey(item.request);
          const ttl = item.ttl || this.defaultTtl;

          pipeline.setex(cacheKey, ttl, JSON.stringify(item.response));
        }
      }

      await pipeline.exec();
      this.logger.debug(`批量设置缓存，共 ${items.length} 项`);
    } catch (error) {
      this.logger.error(`批量设置缓存失败: ${error.message}`);
    }
  }
}
