import { Module } from '@nestjs/common';
import { ProvidersService } from './providers.service';
import { OpenAIProvider } from './impl/openai.provider';
import { AnthropicProvider } from './impl/anthropic.provider';
import { OllamaProvider } from './impl/ollama.provider';
import { CustomProvider } from './impl/custom.provider';

/**
 * 提供商模块
 * 管理所有LLM提供商的注册和服务
 */
@Module({
  providers: [
    ProvidersService,
    OpenAIProvider,
    AnthropicProvider,
    OllamaProvider,
    CustomProvider,
  ],
  exports: [ProvidersService],
})
export class ProvidersModule {}