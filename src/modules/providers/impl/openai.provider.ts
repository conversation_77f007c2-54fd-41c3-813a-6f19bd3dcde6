import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance } from 'axios';
import { Observable } from 'rxjs';
import { BaseLLMProvider } from '../base.provider';
import { ChatCompletionDto, ChatCompletionResponseDto } from '../../../common/dto/chat-completion.dto';
import { ErrorClassifier, ProviderError, ErrorType } from '../../../common/utils/error-categories';

/**
 * OpenAI提供商实现
 * 支持GPT系列模型的聊天完成功能
 */
@Injectable()
export class OpenAIProvider extends BaseLLMProvider {
  private readonly httpClient: AxiosInstance;

  constructor(private readonly configService: ConfigService) {
    const config = configService.get('providers.openai');
    super('OpenAI', config);

    // 创建HTTP客户端
    this.httpClient = axios.create({
      baseURL: config.baseUrl,
      timeout: config.timeout,
      headers: {
        'Authorization': `Bearer ${config.apiKey}`,
        'Content-Type': 'application/json',
      },
    });

    // 请求拦截器
    this.httpClient.interceptors.request.use(
      (config) => {
        this.logger.debug(`发送请求到 OpenAI: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        this.logger.error('OpenAI请求拦截器错误:', error);
        const err = error instanceof Error ? error : new Error(String(error));
        return Promise.reject(err);
      },
    );

    // 响应拦截器
    this.httpClient.interceptors.response.use(
      (response) => {
        this.logger.debug(`OpenAI响应状态: ${response.status}`);
        return response;
      },
      (error) => {
        this.logger.error('OpenAI响应错误:', error.response?.data || error.message);
        return Promise.reject(this.handleError(error));
      },
    );
  }

  /**
   * 聊天完成实现
   */
  async chatCompletion(request: ChatCompletionDto): Promise<ChatCompletionResponseDto> {
    if (!this.isModelSupported(request.model)) {
      throw new HttpException(
        `模型 ${request.model} 不被 OpenAI 提供商支持`,
        HttpStatus.BAD_REQUEST,
      );
    }

    return this.withRetry(async () => {
      const response = await this.httpClient.post('/chat/completions', {
        model: request.model,
        messages: request.messages,
        temperature: request.temperature,
        max_tokens: request.max_tokens,
        top_p: request.top_p,
        frequency_penalty: request.frequency_penalty,
        presence_penalty: request.presence_penalty,
        stop: request.stop,
        user: request.user,
        stream: false, // 非流式响应
      });

      return response.data;
    });
  }

  /**
   * 流式聊天完成
   */
  chatCompletionStream(request: ChatCompletionDto): Observable<string> {
    return new Observable((subscriber) => {
      if (!this.isModelSupported(request.model)) {
        subscriber.error(
          new HttpException(
            `模型 ${request.model} 不被 OpenAI 提供商支持`,
            HttpStatus.BAD_REQUEST,
          ),
        );
        return;
      }

      const streamRequest = async () => {
        try {
          const response = await this.httpClient.post(
            '/chat/completions',
            {
              model: request.model,
              messages: request.messages,
              temperature: request.temperature,
              max_tokens: request.max_tokens,
              top_p: request.top_p,
              frequency_penalty: request.frequency_penalty,
              presence_penalty: request.presence_penalty,
              stop: request.stop,
              user: request.user,
              stream: true,
            },
            {
              responseType: 'stream',
            },
          );

          response.data.on('data', (chunk: Buffer) => {
            const lines = chunk.toString().split('\n');
            
            for (const line of lines) {
              if (line.startsWith('data: ')) {
                const data = line.slice(6).trim();
                
                if (data === '[DONE]') {
                  subscriber.complete();
                  return;
                }

                try {
                  const parsed = JSON.parse(data);
                  const content = parsed.choices?.[0]?.delta?.content;
                  
                  if (content) {
                    subscriber.next(`data: ${JSON.stringify(parsed)}\n\n`);
                  }
                } catch (error) {
                  // 忽略解析错误，继续处理下一行
                  this.logger.error(`解析OpenAI流式响应块失败: ${error.message}`);
                }
              }
            }
          });

          response.data.on('end', () => {
            subscriber.complete();
          });

          response.data.on('error', (error: Error) => {
            subscriber.error(this.handleError(error));
          });

        } catch (error) {
          subscriber.error(this.handleError(error));
        }
      };

      void streamRequest();
    });
  }

  /**
   * 健康检查（优化版）
   */
  async healthCheck(): Promise<boolean> {
    try {
      const response = await this.withHealthCheckRetry(async () => {
        return await this.httpClient.get('/models', {
          timeout: this.config.healthCheckTimeout || 5000,
        });
      });
      
      return response.status === 200;
    } catch (error) {
      const providerError = ErrorClassifier.classify(error, 'OpenAI');
      this.logger.error(`OpenAI健康检查失败: ${providerError.message} (类型: ${providerError.type})`);
      return false;
    }
  }

  /**
   * 获取可用模型列表
   */
  async getModels(): Promise<string[]> {
    try {
      const response = await this.httpClient.get('/models');
      const models = response.data.data
        .filter((model: any) => model.id.includes('gpt'))
        .map((model: any) => model.id);
      
      return models;
    } catch (error) {
      const providerError = ErrorClassifier.classify(error, 'OpenAI');
      this.logger.error(`获取OpenAI模型列表失败: ${providerError.message} (类型: ${providerError.type})`);
      return this.supportedModels;
    }
  }

  /**
   * 错误处理（增强版）
   */
  private handleError(error: any): HttpException {
    const providerError = ErrorClassifier.classify(error, 'OpenAI');
    
    if (providerError.statusCode) {
      switch (providerError.statusCode) {
        case 401:
          return new HttpException(
            'OpenAI API密钥无效或已过期，请检查配置',
            HttpStatus.UNAUTHORIZED,
          );
        case 429:
          return new HttpException(
            'OpenAI API请求频率限制，请稍后重试',
            HttpStatus.TOO_MANY_REQUESTS,
          );
        case 500:
          return new HttpException(
            'OpenAI服务器内部错误，请稍后重试',
            HttpStatus.INTERNAL_SERVER_ERROR,
          );
        case 503:
          return new HttpException(
            'OpenAI服务暂时不可用，请稍后重试',
            HttpStatus.SERVICE_UNAVAILABLE,
          );
        default:
          return new HttpException(
            `OpenAI API错误: ${providerError.message}`,
            providerError.statusCode,
          );
      }
    }

    return new HttpException(
      `OpenAI连接错误: ${providerError.message}`,
      HttpStatus.SERVICE_UNAVAILABLE,
    );
  }
}