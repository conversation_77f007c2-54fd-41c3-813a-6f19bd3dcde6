import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance } from 'axios';
import { Observable } from 'rxjs';
import { BaseLLMProvider } from '../base.provider';
import { ChatCompletionDto, ChatCompletionResponseDto } from '../../../common/dto/chat-completion.dto';

/**
 * 自定义端点提供商实现
 * 支持任何兼容OpenAI API格式的自定义端点
 */
@Injectable()
export class CustomProvider extends BaseLLMProvider {
  private readonly httpClient: AxiosInstance;

  constructor(private readonly configService: ConfigService) {
    const config = configService.get('providers.custom');
    super('Custom', config);

    if (!config.baseUrl) {
      this.logger.warn('自定义端点未配置，将跳过初始化');
      return;
    }

    // 创建HTTP客户端
    this.httpClient = axios.create({
      baseURL: config.baseUrl,
      timeout: config.timeout,
      headers: {
        'Content-Type': 'application/json',
        ...(config.apiKey && { 'Authorization': `Bearer ${config.apiKey}` }),
      },
    });

    // 请求拦截器
    this.httpClient.interceptors.request.use(
      (config) => {
        this.logger.debug(`发送请求到自定义端点: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        this.logger.error('自定义端点请求拦截器错误:', error);
        const err = error instanceof Error ? error : new Error(String(error));
        return Promise.reject(err);
      },
    );

    // 响应拦截器
    this.httpClient.interceptors.response.use(
      (response) => {
        this.logger.debug(`自定义端点响应状态: ${response.status}`);
        return response;
      },
      (error) => {
        this.logger.error('自定义端点响应错误:', error.response?.data || error.message);
        return Promise.reject(this.handleError(error));
      },
    );
  }

  /**
   * 聊天完成实现
   */
  async chatCompletion(request: ChatCompletionDto): Promise<ChatCompletionResponseDto> {
    if (!this.httpClient) {
      throw new HttpException('自定义端点未配置', HttpStatus.SERVICE_UNAVAILABLE);
    }

    return this.withRetry(async () => {
      const response = await this.httpClient.post('/v1/chat/completions', {
        model: request.model,
        messages: request.messages,
        temperature: request.temperature,
        max_tokens: request.max_tokens,
        top_p: request.top_p,
        frequency_penalty: request.frequency_penalty,
        presence_penalty: request.presence_penalty,
        stop: request.stop,
        user: request.user,
        stream: false,
      });

      return response.data;
    });
  }

  /**
   * 流式聊天完成
   */
  chatCompletionStream(request: ChatCompletionDto): Observable<string> {
    return new Observable((subscriber) => {
      if (!this.httpClient) {
        subscriber.error(new HttpException('自定义端点未配置', HttpStatus.SERVICE_UNAVAILABLE));
        return;
      }

      const streamRequest = async () => {
        try {
          const response = await this.httpClient.post(
            '/v1/chat/completions',
            {
              model: request.model,
              messages: request.messages,
              temperature: request.temperature,
              max_tokens: request.max_tokens,
              top_p: request.top_p,
              frequency_penalty: request.frequency_penalty,
              presence_penalty: request.presence_penalty,
              stop: request.stop,
              user: request.user,
              stream: true,
            },
            {
              responseType: 'stream',
            },
          );

          response.data.on('data', (chunk: Buffer) => {
            const lines = chunk.toString().split('\n');
            
            for (const line of lines) {
              if (line.startsWith('data: ')) {
                const data = line.slice(6).trim();
                
                if (data === '[DONE]') {
                  subscriber.complete();
                  return;
                }

                try {
                  const parsed = JSON.parse(data);
                  subscriber.next(`data: ${JSON.stringify(parsed)}\n\n`);
                } catch (error) {
                  // 忽略解析错误，继续处理下一行
                  this.logger.error(`解析自定义端点流式响应块失败: ${error.message}`);
                }
              }
            }
          });

          response.data.on('end', () => {
            subscriber.complete();
          });

          response.data.on('error', (error: Error) => {
            subscriber.error(this.handleError(error));
          });

        } catch (error) {
          subscriber.error(this.handleError(error));
        }
      };

      void streamRequest();
    });
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<boolean> {
    if (!this.httpClient) {
      return false;
    }

    try {
      // 尝试获取模型列表或发送简单请求
      const response = await this.httpClient.get('/v1/models', {
        timeout: 5000,
      });
      return response.status === 200;
    } catch (error) {
      this.logger.error(`自定义端点健康检查失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 获取可用模型列表
   */
  async getModels(): Promise<string[]> {
    if (!this.httpClient) {
      return [];
    }

    try {
      const response = await this.httpClient.get('/v1/models');
      const models = response.data.data?.map((model: any) => model.id) || [];
      return models;
    } catch (error) {
      this.logger.error(`获取自定义端点模型列表失败: ${error.message}`);
      return this.supportedModels;
    }
  }

  /**
   * 错误处理
   */
  private handleError(error: any): HttpException {
    if (error.response) {
      const status = error.response.status;
      const message = error.response.data?.error?.message || error.message;

      switch (status) {
        case 401:
          return new HttpException('自定义端点API密钥无效', HttpStatus.UNAUTHORIZED);
        case 429:
          return new HttpException('自定义端点请求频率限制', HttpStatus.TOO_MANY_REQUESTS);
        case 500:
          return new HttpException('自定义端点服务器内部错误', HttpStatus.INTERNAL_SERVER_ERROR);
        default:
          return new HttpException(`自定义端点API错误: ${message}`, status);
      }
    }

    return new HttpException(
      `自定义端点连接错误: ${error.message}`,
      HttpStatus.SERVICE_UNAVAILABLE,
    );
  }
}