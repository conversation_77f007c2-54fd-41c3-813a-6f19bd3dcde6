import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance } from 'axios';
import { Observable } from 'rxjs';
import { BaseLLMProvider } from '../base.provider';
import { ChatCompletionDto, ChatCompletionResponseDto } from '../../../common/dto/chat-completion.dto';
import { ErrorClassifier } from '../../../common/utils/error-categories';

/**
 * Ollama提供商实现
 * 支持本地部署的Ollama模型服务
 */
@Injectable()
export class OllamaProvider extends BaseLLMProvider {
  private readonly httpClient: AxiosInstance;

  constructor(private readonly configService: ConfigService) {
    const config = configService.get('providers.ollama');
    super('Ollama', config);

    // 创建HTTP客户端
    this.httpClient = axios.create({
      baseURL: config.baseUrl,
      timeout: config.timeout,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // 请求拦截器
    this.httpClient.interceptors.request.use(
      (config) => {
        this.logger.debug(`发送请求到 Ollama: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        this.logger.error('Ollama请求拦截器错误:', error);
        const err = error instanceof Error ? error : new Error(String(error));
        return Promise.reject(err);
      },
    );

    // 响应拦截器
    this.httpClient.interceptors.response.use(
      (response) => {
        this.logger.debug(`Ollama响应状态: ${response.status}`);
        return response;
      },
      (error) => {
        this.logger.error('Ollama响应错误:', error.response?.data || error.message);
        return Promise.reject(this.handleError(error));
      },
    );
  }

  /**
   * 聊天完成实现
   */
  async chatCompletion(request: ChatCompletionDto): Promise<ChatCompletionResponseDto> {
    if (!this.isModelSupported(request.model)) {
      throw new HttpException(
        `模型 ${request.model} 不被 Ollama 提供商支持`,
        HttpStatus.BAD_REQUEST,
      );
    }

    return this.withRetry(async () => {
      // 转换消息格式为Ollama格式
      const prompt = this.convertMessagesToPrompt(request.messages);

      const ollamaRequest = {
        model: request.model,
        prompt,
        stream: false,
        options: {
          temperature: request.temperature,
          top_p: request.top_p,
          stop: request.stop,
          num_predict: request.max_tokens,
        },
      };

      const response = await this.httpClient.post('/api/generate', ollamaRequest);

      // 转换响应格式为OpenAI兼容格式
      return this.convertResponse(response.data, request.model);
    });
  }

  /**
   * 流式聊天完成
   */
  chatCompletionStream(request: ChatCompletionDto): Observable<string> {
    return new Observable((subscriber) => {
      if (!this.isModelSupported(request.model)) {
        subscriber.error(
          new HttpException(
            `模型 ${request.model} 不被 Ollama 提供商支持`,
            HttpStatus.BAD_REQUEST,
          ),
        );
        return;
      }

      const streamRequest = async () => {
        try {
          const prompt = this.convertMessagesToPrompt(request.messages);

          const response = await this.httpClient.post(
            '/api/generate',
            {
              model: request.model,
              prompt,
              stream: true,
              options: {
                temperature: request.temperature,
                top_p: request.top_p,
                stop: request.stop,
                num_predict: request.max_tokens,
              },
            },
            {
              responseType: 'stream',
            },
          );

          response.data.on('data', (chunk: Buffer) => {
            const lines = chunk.toString().split('\n');

            for (const line of lines) {
              if (line.trim()) {
                try {
                  const parsed = JSON.parse(line);

                  if (parsed.response) {
                    // 转换为OpenAI格式的流式响应
                    const openaiFormat = {
                      id: this.generateId(),
                      object: 'chat.completion.chunk',
                      created: Math.floor(Date.now() / 1000),
                      model: request.model,
                      choices: [{
                        index: 0,
                        delta: { content: parsed.response },
                        finish_reason: parsed.done ? 'stop' : null,
                      }],
                    };
                    subscriber.next('data: ' + JSON.stringify(openaiFormat) + '\n\n');
                  }

                  if (parsed.done) {
                    subscriber.next('data: [DONE]\n\n');
                    subscriber.complete();
                  }
                } catch (error) {
                  // 忽略解析错误，继续处理下一行
                  this.logger.error(`解析Ollama流式响应块失败: ${error.message}`);
                }
              }
            }
          });

          response.data.on('end', () => {
            subscriber.complete();
          });

          response.data.on('error', (error: Error) => {
            subscriber.error(this.handleError(error));
          });

        } catch (error) {
          subscriber.error(this.handleError(error));
        }
      };

      void streamRequest();
    });
  }

  /**
   * 健康检查（优化版）
   */
  async healthCheck(): Promise<boolean> {
    try {
      const response = await this.withHealthCheckRetry(async () => {
        return await this.httpClient.get('/api/tags', {
          timeout: this.config.healthCheckTimeout || 5000,
        });
      });

      return response.status === 200;
    } catch (error) {
      const providerError = ErrorClassifier.classify(error, 'Ollama');
      this.logger.error(`Ollama健康检查失败: ${providerError.message} (类型: ${providerError.type})`);
      return false;
    }
  }

  /**
   * 获取可用模型列表
   */
  async getModels(): Promise<string[]> {
    try {
      const response = await this.httpClient.get('/api/tags');
      const models = response.data.models?.map((model: any) => model.name) || [];

      // 过滤出支持的模型
      const supportedModels = models.filter((model: string) =>
        this.supportedModels.includes(model),
      );

      return supportedModels.length > 0 ? supportedModels : this.supportedModels;
    } catch (error) {
      const providerError = ErrorClassifier.classify(error, 'Ollama');
      this.logger.error(`获取Ollama模型列表失败: ${providerError.message} (类型: ${providerError.type})`);
      return this.supportedModels;
    }
  }

  /**
   * 错误处理（增强版）
   */
  private handleError(error: any): HttpException {
    const providerError = ErrorClassifier.classify(error, 'Ollama');

    if (providerError.statusCode) {
      switch (providerError.statusCode) {
        case 400:
          return new HttpException(
            'Ollama请求格式错误，请检查参数',
            HttpStatus.BAD_REQUEST,
          );
        case 404:
          return new HttpException(
            'Ollama模型不存在或API端点未找到',
            HttpStatus.NOT_FOUND,
          );
        case 429:
          return new HttpException(
            'Ollama请求频率限制，请稍后重试',
            HttpStatus.TOO_MANY_REQUESTS,
          );
        case 500:
          return new HttpException(
            'Ollama服务器内部错误，请稍后重试',
            HttpStatus.INTERNAL_SERVER_ERROR,
          );
        case 503:
          return new HttpException(
            'Ollama服务暂时不可用，请稍后重试',
            HttpStatus.SERVICE_UNAVAILABLE,
          );
        default:
          return new HttpException(
            `Ollama API错误: ${providerError.message}`,
            providerError.statusCode,
          );
      }
    }

    return new HttpException(
      `Ollama连接错误: ${providerError.message}`,
      HttpStatus.SERVICE_UNAVAILABLE,
    );
  }

  /**
   * 转换消息格式为Ollama格式
   */
  private convertMessagesToPrompt(messages: any[]): string {
    if (!messages || messages.length === 0) {
      return '';
    }

    // 将OpenAI格式的消息转换为Ollama格式的prompt
    return messages
      .map(msg => {
        const role = msg.role || 'user';
        const content = msg.content || '';

        switch (role) {
          case 'system':
            return `System: ${content}`;
          case 'user':
            return `User: ${content}`;
          case 'assistant':
            return `Assistant: ${content}`;
          default:
            return `${role}: ${content}`;
        }
      })
      .join('\n') + '\nAssistant:';
  }

  /**
   * 转换Ollama响应为OpenAI兼容格式
   */
  private convertResponse(ollamaResponse: any, model: string): ChatCompletionResponseDto {
    const responseText = ollamaResponse.response || '';

    return {
      id: this.generateId('chatcmpl'),
      object: 'chat.completion',
      created: Math.floor(Date.now() / 1000),
      model: model,
      choices: [
        {
          index: 0,
          message: {
            role: 'assistant',
            content: responseText,
          },
          finish_reason: 'stop',
        },
      ],
      usage: {
        prompt_tokens: ollamaResponse.prompt_eval_count || 0,
        completion_tokens: ollamaResponse.eval_count || 0,
        total_tokens: (ollamaResponse.prompt_eval_count || 0) + (ollamaResponse.eval_count || 0),
      },
    };
  }

  /**
   * 生成唯一ID
   */
  protected generateId(prefix: string = 'chatcmpl'): string {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substring(2);
    return `${prefix}-${timestamp}${random}`;
  }
}
