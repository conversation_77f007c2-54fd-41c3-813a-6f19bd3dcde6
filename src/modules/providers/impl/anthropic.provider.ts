import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance } from 'axios';
import { Observable } from 'rxjs';
import { BaseLLMProvider } from '../base.provider';
import {
  ChatCompletionDto,
  ChatCompletionResponseDto,
} from '../../../common/dto/chat-completion.dto';
import { ErrorClassifier, ProviderError, ErrorType } from '../../../common/utils/error-categories';

/**
 * Anthropic Claude提供商实现
 * 支持Claude系列模型的聊天完成功能
 */
@Injectable()
export class AnthropicProvider extends BaseLLMProvider {
  private readonly httpClient: AxiosInstance;

  constructor(private readonly configService: ConfigService) {
    const config = configService.get('providers.anthropic');
    super('Anthropic', config);

    // 创建HTTP客户端
    this.httpClient = axios.create({
      baseURL: config.baseUrl,
      timeout: config.timeout,
      headers: {
        'x-api-key': config.apiKey,
        'Content-Type': 'application/json',
        'anthropic-version': '2023-06-01',
      },
    });

    // 请求拦截器
    this.httpClient.interceptors.request.use(
      (config) => {
        this.logger.debug(`发送请求到 Anthropic: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        this.logger.error('Anthropic请求拦截器错误:', error);
        const err = error instanceof Error ? error : new Error(String(error));
        return Promise.reject(err);
      },
    );

    // 响应拦截器
    this.httpClient.interceptors.response.use(
      (response) => {
        this.logger.debug(`Anthropic响应状态: ${response.status}`);
        return response;
      },
      (error) => {
        this.logger.error('Anthropic响应错误:', error.response?.data || error.message);
        return Promise.reject(this.handleError(error));
      },
    );
  }

  /**
   * 聊天完成实现
   */
  async chatCompletion(request: ChatCompletionDto): Promise<ChatCompletionResponseDto> {
    if (!this.isModelSupported(request.model)) {
      throw new HttpException(
        `模型 ${request.model} 不被 Anthropic 提供商支持`,
        HttpStatus.BAD_REQUEST,
      );
    }

    return this.withRetry(async () => {
      // 转换消息格式为Anthropic格式
      const { system, messages } = this.convertMessages(request.messages);

      const anthropicRequest = {
        model: request.model,
        max_tokens: request.max_tokens || 1000,
        messages,
        system,
        temperature: request.temperature,
        top_p: request.top_p,
        stop_sequences: request.stop,
      };

      const response = await this.httpClient.post('/v1/messages', anthropicRequest);

      // 转换响应格式为OpenAI兼容格式
      return this.convertResponse(response.data, request.model);
    });
  }

  /**
   * 流式聊天完成
   */
  chatCompletionStream(request: ChatCompletionDto): Observable<string> {
    return new Observable((subscriber) => {
      if (!this.isModelSupported(request.model)) {
        subscriber.error(
          new HttpException(
            `模型 ${request.model} 不被 Anthropic 提供商支持`,
            HttpStatus.BAD_REQUEST,
          ),
        );
        return;
      }

      const streamRequest = async () => {
        try {
          const { system, messages } = this.convertMessages(request.messages);

          const response = await this.httpClient.post(
            '/v1/messages',
            {
              model: request.model,
              max_tokens: request.max_tokens || 1000,
              messages,
              system,
              temperature: request.temperature,
              top_p: request.top_p,
              stop_sequences: request.stop,
              stream: true,
            },
            {
              responseType: 'stream',
            },
          );

          response.data.on('data', (chunk: Buffer) => {
            const lines = chunk.toString().split('\n');

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                const data = line.slice(6).trim();

                if (data === '[DONE]') {
                  subscriber.complete();
                  return;
                }

                try {
                  const parsed = JSON.parse(data);

                  if (parsed.type === 'content_block_delta') {
                    const content = parsed.delta?.text;
                    if (content) {
                      // 转换为OpenAI格式的流式响应
                      const openaiFormat = {
                        id: this.generateId(),
                        object: '
