import { Logger } from '@nestjs/common';
import { Observable } from 'rxjs';
import { ILLMProvider, IProviderConfig } from '../../common/interfaces/provider.interface';
import { ChatCompletionDto, ChatCompletionResponseDto } from '../../common/dto/chat-completion.dto';
import { CircuitBreaker, CircuitBreakerConfig } from '../../common/utils/circuit-breaker';
import { ErrorClassifier, ProviderError, ErrorType } from '../../common/utils/error-categories';

/**
 * LLM提供商基础抽象类
 * 提供通用功能实现，具体提供商继承此类
 */
export abstract class BaseLLMProvider implements ILLMProvider {
  protected readonly logger: Logger;
  protected readonly config: IProviderConfig;
  protected readonly circuitBreaker: CircuitBreaker;

  constructor(
    public readonly name: string,
    config: IProviderConfig,
  ) {
    this.logger = new Logger(`${name}Provider`);
    this.config = config;
    
    // 初始化熔断器
    const circuitBreakerConfig: CircuitBreakerConfig = {
      failureThreshold: config.circuitBreaker?.failureThreshold || 5,
      resetTimeout: config.circuitBreaker?.resetTimeout || 60000,
      monitoringPeriod: config.circuitBreaker?.monitoringPeriod || 60000,
      halfOpenMaxCalls: config.circuitBreaker?.halfOpenMaxCalls || 3,
    };
    
    this.circuitBreaker = new CircuitBreaker(name, circuitBreakerConfig);
  }

  /**
   * 获取支持的模型列表
   */
  get supportedModels(): string[] {
    return this.config.models;
  }

  /**
   * 检查模型是否支持
   */
  isModelSupported(model: string): boolean {
    return this.supportedModels.includes(model);
  }

  /**
   * 获取模型列表（默认返回配置中的模型）
   */
  // eslint-disable-next-line @typescript-eslint/require-await
  async getModels(): Promise<string[]> {
    return this.supportedModels;
  }

  /**
   * 健康检查（默认实现）
   */
  async healthCheck(): Promise<boolean> {
    try {
      // 子类可以重写此方法实现具体的健康检查逻辑
      return true;
    } catch (error) {
      this.logger.error(`健康检查失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 聊天完成接口（抽象方法，子类必须实现）
   */
  abstract chatCompletion(
    request: ChatCompletionDto,
  ): Promise<ChatCompletionResponseDto> | Observable<string>;

  /**
   * 重试机制包装器（增强版，支持熔断器）
   */
  protected async withRetry<T>(
    operation: () => Promise<T>,
    maxRetries: number = this.config.maxRetries,
    delay: number = 1000,
  ): Promise<T> {
    return this.circuitBreaker.execute(async () => {
      let lastError: Error;

      for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
          return await operation();
        } catch (error) {
          const providerError = ErrorClassifier.classify(error, this.name);
          lastError = providerError;

          this.logger.warn(
            `第 ${attempt} 次尝试失败: ${providerError.message} (类型: ${providerError.type})`,
          );

          // 如果是不可重试的错误，直接抛出
          if (!providerError.isRetryable()) {
            throw providerError;
          }

          if (attempt < maxRetries) {
            // 指数退避策略，带抖动
            const backoffDelay = delay * Math.pow(2, attempt - 1) + Math.random() * 1000;
            await this.sleep(Math.min(backoffDelay, 30000)); // 最大30秒
          }
        }
      }

      throw lastError!;
    });
  }

  /**
   * 健康检查包装器（带熔断器）
   */
  protected async withHealthCheckRetry<T>(
    operation: () => Promise<T>,
    timeout: number = this.config.healthCheckTimeout || 5000,
  ): Promise<T> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      return await Promise.race([
        operation(),
        new Promise<never>((_, reject) => 
          setTimeout(() => reject(new Error('Health check timeout')), timeout)
        ),
      ]);
    } finally {
      clearTimeout(timeoutId);
    }
  }

  /**
   * 睡眠函数
   */
  protected sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * 生成唯一ID
   */
  protected generateId(prefix: string = 'chatcmpl'): string {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substring(2);
    return `${prefix}-${timestamp}${random}`;
  }

  /**
   * 计算token数量（简单估算）
   */
  protected estimateTokens(text: string): number {
    // 简单的token估算：英文按空格分割，中文按字符计算
    const englishWords = text.match(/[a-zA-Z]+/g)?.length || 0;
    const chineseChars = text.match(/[\u4e00-\u9fff]/g)?.length || 0;
    const otherChars = text.length - englishWords - chineseChars;
    
    return Math.ceil(englishWords * 1.3 + chineseChars * 1.5 + otherChars * 0.5);
  }

  /**
   * 获取熔断器状态
   */
  getCircuitBreakerStatus() {
    return this.circuitBreaker.getStatus();
  }
}