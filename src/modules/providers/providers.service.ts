import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Observable } from 'rxjs';
import { ILLMProvider, RoutingStrategy } from '../../common/interfaces/provider.interface';
import { ChatCompletionDto, ChatCompletionResponseDto } from '../../common/dto/chat-completion.dto';
import { OpenAIProvider } from './impl/openai.provider';
import { AnthropicProvider } from './impl/anthropic.provider';
import { OllamaProvider } from './impl/ollama.provider';
import { CustomProvider } from './impl/custom.provider';

/**
 * 提供商统一调度服务
 * 负责管理所有LLM提供商，实现智能路由和负载均衡
 */
@Injectable()
export class ProvidersService {
  private readonly logger = new Logger(ProvidersService.name);
  private readonly providers: Map<string, ILLMProvider> = new Map();
  private readonly modelToProviders: Map<string, string[]> = new Map();
  private readonly providerWeights: Map<string, number> = new Map();
  private readonly providerConnections: Map<string, number> = new Map();
  private roundRobinIndex = 0;

  constructor(
    private readonly configService: ConfigService,
    private readonly openaiProvider: OpenAIProvider,
    private readonly anthropicProvider: AnthropicProvider,
    private readonly ollamaProvider: OllamaProvider,
    private readonly customProvider: CustomProvider,
  ) {
    this.initializeProviders();
    void this.buildModelMapping();
    this.initializeWeights();
  }

  /**
   * 初始化所有提供商
   */
  private initializeProviders(): void {
    // 注册所有提供商
    this.providers.set('openai', this.openaiProvider);
    this.providers.set('anthropic', this.anthropicProvider);
    this.providers.set('ollama', this.ollamaProvider);
    this.providers.set('custom', this.customProvider);

    // 初始化连接计数
    for (const [name] of this.providers) {
      this.providerConnections.set(name, 0);
    }

    this.logger.log(`已初始化 ${this.providers.size} 个提供商`);
  }

  /**
   * 构建模型到提供商的映射关系
   */
  private async buildModelMapping(): Promise<void> {
    for (const [name, provider] of this.providers) {
      try {
        const models = await provider.getModels();
        for (const model of models) {
          if (!this.modelToProviders.has(model)) {
            this.modelToProviders.set(model, []);
          }
          this.modelToProviders.get(model).push(name);
        }
        this.logger.log(`提供商 ${name} 支持 ${models.length} 个模型`);
      } catch (error) {
        this.logger.error(`获取提供商 ${name} 模型列表失败: ${error.message}`);
      }
    }
  }

  /**
   * 初始化提供商权重
   */
  private initializeWeights(): void {
    // 默认权重配置
    this.providerWeights.set('openai', 3);
    this.providerWeights.set('anthropic', 2);
    this.providerWeights.set('ollama', 1);
    this.providerWeights.set('custom', 1);
  }

  /**
   * 聊天完成接口
   */
  async chatCompletion(request: ChatCompletionDto): Promise<ChatCompletionResponseDto> {
    const provider = await this.selectProvider(request.model);

    if (!provider) {
      throw new HttpException(`未找到支持模型 ${request.model} 的提供商`, HttpStatus.BAD_REQUEST);
    }

    try {
      // 增加连接计数
      this.incrementConnections(provider.name);

      const startTime = Date.now();
      const result = (await provider.chatCompletion(request)) as ChatCompletionResponseDto;
      const duration = Date.now() - startTime;

      this.logger.log(
        `提供商 ${provider.name} 完成请求，耗时 ${duration}ms，模型: ${request.model}`,
      );

      // 记录token使用情况
      if ('usage' in result) {
        await this.recordTokenUsage(provider.name, request.model, result.usage);
      }

      return result;
    } catch (error) {
      this.logger.error(`提供商 ${provider.name} 请求失败: ${error.message}`);

      // 尝试降级到其他提供商
      const fallbackProvider = await this.selectFallbackProvider(request.model, provider.name);
      if (fallbackProvider) {
        this.logger.log(`降级到提供商 ${fallbackProvider.name}`);
        return this.chatCompletion(request);
      }

      throw error;
    } finally {
      // 减少连接计数
      this.decrementConnections(provider.name);
    }
  }

  /**
   * 流式聊天完成接口
   */
  chatCompletionStream(request: ChatCompletionDto): Observable<string> {
    return new Observable((subscriber) => {
      this.selectProvider(request.model)
        .then((provider) => {
          if (!provider) {
            subscriber.error(
              new HttpException(`未找到支持模型 ${request.model} 的提供商`, HttpStatus.BAD_REQUEST),
            );
            return;
          }

          // 增加连接计数
          this.incrementConnections(provider.name);

          const startTime = Date.now();
          let tokenCount = 0;

          // 获取流式响应
          let stream: Observable<string>;

          if (typeof provider['chatCompletionStream'] === 'function') {
            stream = provider['chatCompletionStream'](request);
          } else {
            stream = new Observable((obs) => {
              (provider.chatCompletion(request) as Promise<ChatCompletionResponseDto>)
                .then((result) => {
                  obs.next(`data: ${JSON.stringify(result)}\n\n`);
                  obs.next('data: [DONE]\n\n');
                  obs.complete();
                })
                .catch((error) => obs.error(error));
            });
          }

          stream.subscribe({
            next: (chunk) => {
              subscriber.next(chunk);
              // 简单估算token数量
              tokenCount += this.estimateTokensFromChunk(chunk);
            },
            complete: () => {
              const duration = Date.now() - startTime;
              this.logger.log(
                `提供商 ${provider.name} 流式请求完成，耗时 ${duration}ms，估算tokens: ${tokenCount}`,
              );

              // 记录token使用情况
              this.recordTokenUsage(provider.name, request.model, {
                prompt_tokens: this.estimatePromptTokens(request.messages),
                completion_tokens: tokenCount,
                total_tokens: this.estimatePromptTokens(request.messages) + tokenCount,
              }).catch((error) => {
                this.logger.error(`记录token使用情况失败: ${error.message}`);
              });

              this.decrementConnections(provider.name);
              subscriber.complete();
            },
            error: (error) => {
              this.logger.error(`提供商 ${provider.name} 流式请求失败: ${error.message}`);
              this.decrementConnections(provider.name);
              subscriber.error(error);
            },
          });
        })
        .catch((error) => {
          subscriber.error(error);
        });
    });
  }

  /**
   * 选择提供商（智能路由）
   */
  public async selectProvider(model: string): Promise<ILLMProvider | null> {
    const availableProviders = this.modelToProviders.get(model);

    if (!availableProviders || availableProviders.length === 0) {
      return null;
    }

    // 过滤健康的提供商
    const healthyProviders = [];
    for (const providerName of availableProviders) {
      const provider = this.providers.get(providerName);
      if (provider && (await provider.healthCheck())) {
        healthyProviders.push(providerName);
      }
    }

    if (healthyProviders.length === 0) {
      return null;
    }

    // 根据策略选择提供商
    const strategy = this.configService.get<RoutingStrategy>(
      'routing.strategy',
      RoutingStrategy.ROUND_ROBIN,
    );
    const selectedProviderName = this.selectByStrategy(healthyProviders, strategy);

    return this.providers.get(selectedProviderName) || null;
  }

  /**
   * 根据策略选择提供商
   */
  private selectByStrategy(providers: string[], strategy: RoutingStrategy): string {
    switch (strategy) {
      case RoutingStrategy.ROUND_ROBIN:
        return this.selectRoundRobin(providers);

      case RoutingStrategy.WEIGHTED_ROUND_ROBIN:
        return this.selectWeightedRoundRobin(providers);

      case RoutingStrategy.LEAST_CONNECTIONS:
        return this.selectLeastConnections(providers);

      case RoutingStrategy.RANDOM:
        return providers[Math.floor(Math.random() * providers.length)];

      default:
        return providers[0];
    }
  }

  /**
   * 轮询选择
   */
  private selectRoundRobin(providers: string[]): string {
    const selected = providers[this.roundRobinIndex % providers.length];
    this.roundRobinIndex++;
    return selected;
  }

  /**
   * 加权轮询选择
   */
  private selectWeightedRoundRobin(providers: string[]): string {
    const totalWeight = providers.reduce((sum, provider) => {
      return sum + (this.providerWeights.get(provider) || 1);
    }, 0);

    let random = Math.random() * totalWeight;

    for (const provider of providers) {
      const weight = this.providerWeights.get(provider) || 1;
      random -= weight;
      if (random <= 0) {
        return provider;
      }
    }

    return providers[0];
  }

  /**
   * 最少连接选择
   */
  private selectLeastConnections(providers: string[]): string {
    let minConnections = Infinity;
    let selectedProvider = providers[0];

    for (const provider of providers) {
      const connections = this.providerConnections.get(provider) || 0;
      if (connections < minConnections) {
        minConnections = connections;
        selectedProvider = provider;
      }
    }

    return selectedProvider;
  }

  /**
   * 选择降级提供商
   */
  private async selectFallbackProvider(
    model: string,
    excludeProvider: string,
  ): Promise<ILLMProvider | null> {
    const availableProviders = this.modelToProviders
      .get(model)
      ?.filter((p) => p !== excludeProvider);

    if (!availableProviders || availableProviders.length === 0) {
      return null;
    }

    // 选择第一个健康的提供商作为降级选项
    for (const providerName of availableProviders) {
      const provider = this.providers.get(providerName);
      if (provider && (await provider.healthCheck())) {
        return provider;
      }
    }

    return null;
  }

  /**
   * 增加连接计数
   */
  private incrementConnections(providerName: string): void {
    const current = this.providerConnections.get(providerName) || 0;
    this.providerConnections.set(providerName, current + 1);
  }

  /**
   * 减少连接计数
   */
  private decrementConnections(providerName: string): void {
    const current = this.providerConnections.get(providerName) || 0;
    this.providerConnections.set(providerName, Math.max(0, current - 1));
  }

  /**
   * 记录token使用情况
   */

  // eslint-disable-next-line
  private async recordTokenUsage(
    provider: string,
    model: string,
    usage: { prompt_tokens: number; completion_tokens: number; total_tokens: number },
  ): Promise<void> {
    try {
      // 这里可以将使用情况记录到数据库
      this.logger.debug(
        `Token使用记录 - 提供商: ${provider}, 模型: ${model}, ` +
          `输入: ${usage.prompt_tokens}, 输出: ${usage.completion_tokens}, 总计: ${usage.total_tokens}`,
      );
    } catch (error) {
      this.logger.error(`记录token使用情况失败: ${error.message}`);
    }
  }

  /**
   * 从流式响应块估算token数量
   */
  private estimateTokensFromChunk(chunk: string): number {
    try {
      if (chunk.startsWith('data: ')) {
        const data = chunk.slice(6).trim();
        if (data !== '[DONE]') {
          const parsed = JSON.parse(data);
          const content = parsed.choices?.[0]?.delta?.content || '';
          return this.estimateTokens(content);
        }
      }
    } catch (error) {
      // 忽略解析错误
      this.logger.error(`解析流式响应块失败: ${error.message}`);
    }
    return 0;
  }

  /**
   * 估算提示词token数量
   */
  private estimatePromptTokens(messages: any[]): number {
    const totalText = messages.map((m) => m.content).join(' ');
    return this.estimateTokens(totalText);
  }

  /**
   * 简单的token估算
   */
  private estimateTokens(text: string): number {
    const englishWords = text.match(/[a-zA-Z]+/g)?.length || 0;
    const chineseChars = text.match(/[\u4e00-\u9fff]/g)?.length || 0;
    const otherChars = text.length - englishWords - chineseChars;

    return Math.ceil(englishWords * 1.3 + chineseChars * 1.5 + otherChars * 0.5);
  }

  /**
   * 获取所有提供商状态
   */
  async getProvidersStatus(): Promise<any> {
    const status = {};

    for (const [name, provider] of this.providers) {
      const isHealthy = await provider.healthCheck();
      const connections = this.providerConnections.get(name) || 0;
      const models = await provider.getModels();

      status[name] = {
        name: provider.name,
        healthy: isHealthy,
        connections,
        supportedModels: models,
        weight: this.providerWeights.get(name) || 1,
      };
    }

    return status;
  }

  /**
   * 获取模型到提供商的映射
   */
  getModelMapping(): Record<string, string[]> {
    const mapping = {};
    for (const [model, providers] of this.modelToProviders) {
      mapping[model] = providers;
    }
    return mapping;
  }

  /**
   * 更新提供商权重
   */
  updateProviderWeight(providerName: string, weight: number): void {
    if (this.providers.has(providerName)) {
      this.providerWeights.set(providerName, weight);
      this.logger.log(`已更新提供商 ${providerName} 权重为 ${weight}`);
    }
  }

  /**
   * 刷新模型映射
   */
  async refreshModelMapping(): Promise<void> {
    this.modelToProviders.clear();
    await this.buildModelMapping();
    this.logger.log('已刷新模型映射');
  }
}
