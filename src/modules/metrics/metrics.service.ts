import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as promClient from 'prom-client';

/**
 * 监控指标服务
 * 使用Prometheus客户端收集和暴露应用指标
 */
@Injectable()
export class MetricsService {
  private readonly logger = new Logger(MetricsService.name);
  private readonly register: promClient.Registry;
  private readonly enabled: boolean;

  // 指标定义
  private readonly httpRequestsTotal: promClient.Counter<string>;
  private readonly httpRequestDuration: promClient.Histogram<string>;
  private readonly llmRequestsTotal: promClient.Counter<string>;
  private readonly llmRequestDuration: promClient.Histogram<string>;
  private readonly llmTokensTotal: promClient.Counter<string>;
  private readonly llmCostTotal: promClient.Counter<string>;
  private readonly activeConnections: promClient.Gauge<string>;
  private readonly cacheHitRate: promClient.Gauge<string>;
  private readonly rateLimitHits: promClient.Counter<string>;

  constructor(private readonly configService: ConfigService) {
    this.enabled = this.configService.get('app.metrics.enabled', true);
    
    if (!this.enabled) {
      this.logger.warn('监控指标已禁用');
      return;
    }

    // 创建注册表
    this.register = new promClient.Registry();
    
    // 添加默认指标
    promClient.collectDefaultMetrics({
      register: this.register,
      prefix: 'llm_gateway_',
    });

    // HTTP请求总数
    this.httpRequestsTotal = new promClient.Counter({
      name: 'llm_gateway_http_requests_total',
      help: 'HTTP请求总数',
      labelNames: ['method', 'route', 'status_code'],
      registers: [this.register],
    });

    // HTTP请求持续时间
    this.httpRequestDuration = new promClient.Histogram({
      name: 'llm_gateway_http_request_duration_seconds',
      help: 'HTTP请求持续时间（秒）',
      labelNames: ['method', 'route', 'status_code'],
      buckets: [0.1, 0.5, 1, 2, 5, 10, 30, 60],
      registers: [this.register],
    });

    // LLM请求总数
    this.llmRequestsTotal = new promClient.Counter({
      name: 'llm_gateway_llm_requests_total',
      help: 'LLM请求总数',
      labelNames: ['provider', 'model', 'status'],
      registers: [this.register],
    });

    // LLM请求持续时间
    this.llmRequestDuration = new promClient.Histogram({
      name: 'llm_gateway_llm_request_duration_seconds',
      help: 'LLM请求持续时间（秒）',
      labelNames: ['provider', 'model'],
      buckets: [1, 5, 10, 30, 60, 120, 300],
      registers: [this.register],
    });

    // Token使用总数
    this.llmTokensTotal = new promClient.Counter({
      name: 'llm_gateway_llm_tokens_total',
      help: 'LLM Token使用总数',
      labelNames: ['provider', 'model', 'type'],
      registers: [this.register],
    });

    // 费用总计
    this.llmCostTotal = new promClient.Counter({
      name: 'llm_gateway_llm_cost_total',
      help: 'LLM调用费用总计',
      labelNames: ['provider', 'model'],
      registers: [this.register],
    });

    // 活跃连接数
    this.activeConnections = new promClient.Gauge({
      name: 'llm_gateway_active_connections',
      help: '当前活跃连接数',
      labelNames: ['provider'],
      registers: [this.register],
    });

    // 缓存命中率
    this.cacheHitRate = new promClient.Gauge({
      name: 'llm_gateway_cache_hit_rate',
      help: '缓存命中率',
      registers: [this.register],
    });

    // 速率限制命中次数
    this.rateLimitHits = new promClient.Counter({
      name: 'llm_gateway_rate_limit_hits_total',
      help: '速率限制命中次数',
      labelNames: ['api_key_id', 'limit_type'],
      registers: [this.register],
    });

    this.logger.log('监控指标服务已初始化');
  }

  /**
   * 记录HTTP请求指标
   */
  recordHttpRequest(
    method: string,
    route: string,
    statusCode: number,
    duration: number,
  ): void {
    if (!this.enabled) return;

    try {
      this.httpRequestsTotal
        .labels(method, route, statusCode.toString())
        .inc();

      this.httpRequestDuration
        .labels(method, route, statusCode.toString())
        .observe(duration / 1000);
    } catch (error) {
      this.logger.error(`记录HTTP请求指标失败: ${error.message}`);
    }
  }

  /**
   * 记录LLM请求指标
   */
  recordLlmRequest(
    provider: string,
    model: string,
    status: 'success' | 'error' | 'timeout',
    duration: number,
    promptTokens: number = 0,
    completionTokens: number = 0,
    cost: number = 0,
  ): void {
    if (!this.enabled) return;

    try {
      // 请求总数
      this.llmRequestsTotal
        .labels(provider, model, status)
        .inc();

      // 请求持续时间
      if (status === 'success') {
        this.llmRequestDuration
          .labels(provider, model)
          .observe(duration / 1000);
      }

      // Token使用量
      if (promptTokens > 0) {
        this.llmTokensTotal
          .labels(provider, model, 'prompt')
          .inc(promptTokens);
      }

      if (completionTokens > 0) {
        this.llmTokensTotal
          .labels(provider, model, 'completion')
          .inc(completionTokens);
      }

      // 费用
      if (cost > 0) {
        this.llmCostTotal
          .labels(provider, model)
          .inc(cost);
      }
    } catch (error) {
      this.logger.error(`记录LLM请求指标失败: ${error.message}`);
    }
  }

  /**
   * 更新活跃连接数
   */
  updateActiveConnections(provider: string, count: number): void {
    if (!this.enabled) return;

    try {
      this.activeConnections
        .labels(provider)
        .set(count);
    } catch (error) {
      this.logger.error(`更新活跃连接数失败: ${error.message}`);
    }
  }

  /**
   * 更新缓存命中率
   */
  updateCacheHitRate(hitRate: number): void {
    if (!this.enabled) return;

    try {
      this.cacheHitRate.set(hitRate);
    } catch (error) {
      this.logger.error(`更新缓存命中率失败: ${error.message}`);
    }
  }

  /**
   * 记录速率限制命中
   */
  recordRateLimitHit(apiKeyId: string, limitType: 'requests' | 'tokens'): void {
    if (!this.enabled) return;

    try {
      this.rateLimitHits
        .labels(apiKeyId, limitType)
        .inc();
    } catch (error) {
      this.logger.error(`记录速率限制命中失败: ${error.message}`);
    }
  }

  /**
   * 获取所有指标
   */
  async getMetrics(): Promise<string> {
    if (!this.enabled) {
      return '# 监控指标已禁用\n';
    }

    try {
      return await this.register.metrics();
    } catch (error) {
      this.logger.error(`获取指标失败: ${error.message}`);
      return '# 获取指标时发生错误\n';
    }
  }

  /**
   * 获取指标摘要
   */
  async getMetricsSummary(): Promise<{
    httpRequests: number;
    llmRequests: number;
    totalTokens: number;
    totalCost: number;
    cacheHitRate: number;
    activeConnections: number;
  }> {
    if (!this.enabled) {
      return {
        httpRequests: 0,
        llmRequests: 0,
        totalTokens: 0,
        totalCost: 0,
        cacheHitRate: 0,
        activeConnections: 0,
      };
    }

    try {
      const metrics = await this.register.getMetricsAsJSON();
      
      const httpRequests = this.getMetricValue(metrics, 'llm_gateway_http_requests_total');
      const llmRequests = this.getMetricValue(metrics, 'llm_gateway_llm_requests_total');
      const totalTokens = this.getMetricValue(metrics, 'llm_gateway_llm_tokens_total');
      const totalCost = this.getMetricValue(metrics, 'llm_gateway_llm_cost_total');
      const cacheHitRate = this.getMetricValue(metrics, 'llm_gateway_cache_hit_rate');
      const activeConnections = this.getMetricValue(metrics, 'llm_gateway_active_connections');

      return {
        httpRequests,
        llmRequests,
        totalTokens,
        totalCost,
        cacheHitRate,
        activeConnections,
      };
    } catch (error) {
      this.logger.error(`获取指标摘要失败: ${error.message}`);
      return {
        httpRequests: 0,
        llmRequests: 0,
        totalTokens: 0,
        totalCost: 0,
        cacheHitRate: 0,
        activeConnections: 0,
      };
    }
  }

  /**
   * 重置指标
   */
  resetMetrics(): void {
    if (!this.enabled) return;

    try {
      this.register.resetMetrics();
      this.logger.log('已重置所有指标');
    } catch (error) {
      this.logger.error(`重置指标失败: ${error.message}`);
    }
  }

  /**
   * 从指标数组中获取指标值
   */
  private getMetricValue(metrics: any[], metricName: string): number {
    const metric = metrics.find(m => m.name === metricName);
    if (!metric || !metric.values || metric.values.length === 0) {
      return 0;
    }

    // 对于Counter类型，累加所有值
    if (metric.type === 'counter') {
      return metric.values.reduce((sum: number, v: any) => sum + (v.value || 0), 0);
    }

    // 对于Gauge类型，返回最新值
    if (metric.type === 'gauge') {
      return metric.values[metric.values.length - 1]?.value || 0;
    }

    // 对于Histogram类型，返回总计数
    if (metric.type === 'histogram') {
      const countValue = metric.values.find((v: any) => v.metricName?.endsWith('_count'));
      return countValue?.value || 0;
    }

    return 0;
  }

  /**
   * 创建自定义指标
   */
  createCustomCounter(
    name: string,
    help: string,
    labelNames: string[] = [],
  ): promClient.Counter<string> | null {
    if (!this.enabled) return null;

    try {
      const counter = new promClient.Counter({
        name: `llm_gateway_${name}`,
        help,
        labelNames,
        registers: [this.register],
      });

      this.logger.log(`创建自定义计数器指标: ${name}`);
      return counter;
    } catch (error) {
      this.logger.error(`创建自定义指标失败: ${error.message}`);
      return null;
    }
  }

  /**
   * 创建自定义仪表盘
   */
  createCustomGauge(
    name: string,
    help: string,
    labelNames: string[] = [],
  ): promClient.Gauge<string> | null {
    if (!this.enabled) return null;

    try {
      const gauge = new promClient.Gauge({
        name: `llm_gateway_${name}`,
        help,
        labelNames,
        registers: [this.register],
      });

      this.logger.log(`创建自定义仪表盘指标: ${name}`);
      return gauge;
    } catch (error) {
      this.logger.error(`创建自定义指标失败: ${error.message}`);
      return null;
    }
  }

  /**
   * 健康检查
   */
  healthCheck(): boolean {
    return this.enabled;
  }
}