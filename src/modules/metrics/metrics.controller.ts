import { Controller, Get, Header } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { MetricsService } from './metrics.service';

/**
 * 监控指标控制器
 * 提供Prometheus指标端点
 */
@ApiTags('Metrics')
@Controller('metrics')
export class MetricsController {
  constructor(private readonly metricsService: MetricsService) {}

  /**
   * 获取Prometheus格式的指标
   */
  @Get()
  @Header('Content-Type', 'text/plain; version=0.0.4; charset=utf-8')
  @ApiOperation({
    summary: '获取Prometheus指标',
    description: '返回Prometheus格式的应用监控指标',
  })
  @ApiResponse({
    status: 200,
    description: 'Prometheus指标数据',
    content: {
      'text/plain': {
        example: `# HELP llm_gateway_http_requests_total HTTP请求总数
# TYPE llm_gateway_http_requests_total counter
llm_gateway_http_requests_total{method="POST",route="/v1/chat/completions",status_code="200"} 42`,
      },
    },
  })
  async getMetrics(): Promise<string> {
    return this.metricsService.getMetrics();
  }

  /**
   * 获取指标摘要
   */
  @Get('summary')
  @ApiOperation({
    summary: '获取指标摘要',
    description: '返回主要指标的汇总信息',
  })
  @ApiResponse({
    status: 200,
    description: '指标摘要数据',
  })
  async getMetricsSummary() {
    return this.metricsService.getMetricsSummary();
  }
}