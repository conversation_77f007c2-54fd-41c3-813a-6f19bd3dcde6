import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AuthService } from './auth.service';
import { ApiKeyGuard } from './guards/api-key.guard';
import { ApiKeyEntity } from './entities/api-key.entity';
import { UsageLogEntity } from './entities/usage-log.entity';

/**
 * 认证模块
 * 提供API密钥管理和认证功能
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([ApiKeyEntity, UsageLogEntity]),
  ],
  providers: [AuthService, ApiKeyGuard],
  exports: [AuthService, ApiKeyGuard],
})
export class AuthModule {}