import { Injectable, Logger, UnauthorizedException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';
import { ApiKeyEntity } from './entities/api-key.entity';
import { UsageLogEntity } from './entities/usage-log.entity';

/**
 * 认证服务
 * 负责API密钥的验证、配额检查和使用记录
 */
@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    @InjectRepository(ApiKeyEntity)
    private readonly apiKeyRepository: Repository<ApiKeyEntity>,
    @InjectRepository(UsageLogEntity)
    private readonly usageLogRepository: Repository<UsageLogEntity>,
    private readonly configService: ConfigService,
  ) {}

  /**
   * 验证API密钥
   */
  async validateApiKey(key: string): Promise<ApiKeyEntity> {
    if (!key) {
      throw new UnauthorizedException('API密钥不能为空');
    }

    const apiKey = await this.apiKeyRepository.findOne({
      where: { key, enabled: true },
    });

    if (!apiKey) {
      throw new UnauthorizedException('无效的API密钥');
    }

    // 检查是否过期
    if (apiKey.expiresAt && apiKey.expiresAt < new Date()) {
      throw new UnauthorizedException('API密钥已过期');
    }

    // 更新最后使用时间
    apiKey.lastUsedAt = new Date();
    await this.apiKeyRepository.save(apiKey);

    return apiKey;
  }

  /**
   * 检查模型访问权限
   */
  checkModelAccess(apiKey: ApiKeyEntity, model: string): void {
    if (apiKey.allowedModels && apiKey.allowedModels.length > 0) {
      if (!apiKey.allowedModels.includes(model)) {
        throw new ForbiddenException(`API密钥无权访问模型: ${model}`);
      }
    }
  }

  /**
   * 检查IP访问权限
   */
  checkIpAccess(apiKey: ApiKeyEntity, clientIp: string): void {
    if (apiKey.allowedIps && apiKey.allowedIps.length > 0) {
      if (!apiKey.allowedIps.includes(clientIp)) {
        throw new ForbiddenException(`IP地址 ${clientIp} 无权访问此API密钥`);
      }
    }
  }

  /**
   * 检查配额限制
   */
  checkQuota(apiKey: ApiKeyEntity, estimatedTokens: number = 0): void {
    // 检查月度token配额
    if (apiKey.monthlyTokenUsed + estimatedTokens > apiKey.monthlyTokenQuota) {
      throw new ForbiddenException('月度token配额已用完');
    }

    // 检查月度费用限制
    const estimatedCost = this.calculateCost('gpt-3.5-turbo', estimatedTokens, 0);
    if (apiKey.monthlyCost + estimatedCost > apiKey.monthlyCostLimit) {
      throw new ForbiddenException('月度费用限制已达到');
    }
  }

  /**
   * 记录使用情况
   */
  async recordUsage(
    apiKey: ApiKeyEntity,
    model: string,
    provider: string,
    promptTokens: number,
    completionTokens: number,
    responseTime: number,
    status: 'success' | 'error' | 'timeout',
    clientIp?: string,
    userAgent?: string,
    requestId?: string,
    errorMessage?: string,
  ): Promise<void> {
    const totalTokens = promptTokens + completionTokens;
    const cost = this.calculateCost(model, promptTokens, completionTokens);

    // 创建使用日志
    const usageLog = this.usageLogRepository.create({
      apiKeyId: apiKey.id,
      model,
      provider,
      promptTokens,
      completionTokens,
      totalTokens,
      cost,
      responseTime,
      status,
      clientIp,
      userAgent,
      requestId,
      errorMessage,
    });

    await this.usageLogRepository.save(usageLog);

    // 更新API密钥统计信息
    if (status === 'success') {
      apiKey.totalRequests += 1;
      apiKey.totalTokens += totalTokens;
      apiKey.monthlyTokenUsed += totalTokens;
      apiKey.monthlyCost += cost;
      
      await this.apiKeyRepository.save(apiKey);
    }

    this.logger.debug(
      `记录使用情况 - API密钥: ${apiKey.id}, 模型: ${model}, ` +
      `tokens: ${totalTokens}, 费用: ${cost}, 状态: ${status}`,
    );
  }

  /**
   * 创建新的API密钥
   */
  async createApiKey(
    userId: string,
    name: string,
    description?: string,
    allowedModels?: string[],
    allowedIps?: string[],
    rateLimit: number = 1000,
    monthlyTokenQuota: number = 100000,
    monthlyCostLimit: number = 100,
    expiresAt?: Date,
  ): Promise<ApiKeyEntity> {
    const key = this.generateApiKey();

    const apiKey = this.apiKeyRepository.create({
      key,
      name,
      userId,
      description,
      allowedModels,
      allowedIps,
      rateLimit,
      monthlyTokenQuota,
      monthlyCostLimit,
      expiresAt,
    });

    await this.apiKeyRepository.save(apiKey);

    this.logger.log(`创建新API密钥 - 用户: ${userId}, 名称: ${name}`);
    return apiKey;
  }

  /**
   * 更新API密钥
   */
  async updateApiKey(
    keyId: string,
    updates: Partial<ApiKeyEntity>,
  ): Promise<ApiKeyEntity> {
    const apiKey = await this.apiKeyRepository.findOne({ where: { id: keyId } });
    
    if (!apiKey) {
      throw new UnauthorizedException('API密钥不存在');
    }

    Object.assign(apiKey, updates);
    await this.apiKeyRepository.save(apiKey);

    this.logger.log(`更新API密钥 - ID: ${keyId}`);
    return apiKey;
  }

  /**
   * 删除API密钥
   */
  async deleteApiKey(keyId: string): Promise<void> {
    const result = await this.apiKeyRepository.delete(keyId);
    
    if (result.affected === 0) {
      throw new UnauthorizedException('API密钥不存在');
    }

    this.logger.log(`删除API密钥 - ID: ${keyId}`);
  }

  /**
   * 获取用户的API密钥列表
   */
  async getUserApiKeys(userId: string): Promise<ApiKeyEntity[]> {
    return this.apiKeyRepository.find({
      where: { userId },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * 获取API密钥使用统计
   */
  async getUsageStats(
    apiKeyId: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<any> {
    const queryBuilder = this.usageLogRepository
      .createQueryBuilder('log')
      .where('log.apiKeyId = :apiKeyId', { apiKeyId });

    if (startDate) {
      queryBuilder.andWhere('log.createdAt >= :startDate', { startDate });
    }

    if (endDate) {
      queryBuilder.andWhere('log.createdAt <= :endDate', { endDate });
    }

    const logs = await queryBuilder.getMany();

    // 计算统计信息
    const stats = {
      totalRequests: logs.length,
      successfulRequests: logs.filter(log => log.status === 'success').length,
      errorRequests: logs.filter(log => log.status === 'error').length,
      totalTokens: logs.reduce((sum, log) => sum + log.totalTokens, 0),
      totalCost: logs.reduce((sum, log) => sum + Number(log.cost), 0),
      averageResponseTime: logs.length > 0 
        ? logs.reduce((sum, log) => sum + log.responseTime, 0) / logs.length 
        : 0,
      modelUsage: this.groupBy(logs, 'model'),
      providerUsage: this.groupBy(logs, 'provider'),
      dailyUsage: this.groupByDate(logs),
    };

    return stats;
  }

  /**
   * 重置月度使用统计
   */
  async resetMonthlyUsage(): Promise<void> {
    await this.apiKeyRepository.update(
      {},
      {
        monthlyTokenUsed: 0,
        monthlyCost: 0,
      },
    );

    this.logger.log('已重置所有API密钥的月度使用统计');
  }

  /**
   * 生成API密钥
   */
  private generateApiKey(): string {
    const prefix = 'llm-gw';
    const randomBytes = crypto.randomBytes(24).toString('hex');
    return `${prefix}-${randomBytes}`;
  }

  /**
   * 计算费用
   */
  private calculateCost(model: string, promptTokens: number, completionTokens: number): number {
    // 简化的费用计算，实际应该根据不同模型的定价
    const rates = {
      'gpt-4': { prompt: 0.03, completion: 0.06 },
      'gpt-4-turbo': { prompt: 0.01, completion: 0.03 },
      'gpt-3.5-turbo': { prompt: 0.0015, completion: 0.002 },
      'claude-3-opus': { prompt: 0.015, completion: 0.075 },
      'claude-3-sonnet': { prompt: 0.003, completion: 0.015 },
      'claude-3-haiku': { prompt: 0.00025, completion: 0.00125 },
    };

    const rate = rates[model] || rates['gpt-3.5-turbo'];
    return (promptTokens * rate.prompt + completionTokens * rate.completion) / 1000;
  }

  /**
   * 按字段分组
   */
  private groupBy(array: any[], key: string): Record<string, number> {
    return array.reduce((groups, item) => {
      const value = item[key];
      groups[value] = (groups[value] || 0) + 1;
      return groups;
    }, {});
  }

  /**
   * 按日期分组
   */
  private groupByDate(logs: UsageLogEntity[]): Record<string, number> {
    return logs.reduce((groups, log) => {
      const date = log.createdAt.toISOString().split('T')[0];
      groups[date] = (groups[date] || 0) + 1;
      return groups;
    }, {});
  }
}