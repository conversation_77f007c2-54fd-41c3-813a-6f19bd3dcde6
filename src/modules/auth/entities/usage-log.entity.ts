import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  Index,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ApiKeyEntity } from './api-key.entity';

/**
 * 使用日志实体
 * 记录每次API调用的详细信息
 */
@Entity('usage_logs')
@Index(['apiKeyId', 'createdAt'])
@Index(['model', 'createdAt'])
@Index(['provider', 'createdAt'])
export class UsageLogEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid', comment: 'API密钥ID' })
  apiKeyId: string;

  @Column({ type: 'varchar', length: 100, comment: '使用的模型' })
  model: string;

  @Column({ type: 'varchar', length: 50, comment: '提供商名称' })
  provider: string;

  @Column({ type: 'integer', comment: '输入token数量' })
  promptTokens: number;

  @Column({ type: 'integer', comment: '输出token数量' })
  completionTokens: number;

  @Column({ type: 'integer', comment: '总token数量' })
  totalTokens: number;

  @Column({ type: 'decimal', precision: 10, scale: 6, comment: '本次调用费用' })
  cost: number;

  @Column({ type: 'integer', comment: '响应时间(毫秒)' })
  responseTime: number;

  @Column({ type: 'varchar', length: 20, comment: '请求状态' })
  status: 'success' | 'error' | 'timeout';

  @Column({ type: 'text', nullable: true, comment: '错误信息' })
  errorMessage?: string;

  @Column({ type: 'inet', nullable: true, comment: '客户端IP地址' })
  clientIp?: string;

  @Column({ type: 'varchar', length: 500, nullable: true, comment: '用户代理' })
  userAgent?: string;

  @Column({ type: 'varchar', length: 100, nullable: true, comment: '请求ID' })
  requestId?: string;

  @CreateDateColumn({ comment: '创建时间' })
  createdAt: Date;

  // 关联关系
  @ManyToOne(() => ApiKeyEntity)
  @JoinColumn({ name: 'apiKeyId' })
  apiKey: ApiKeyEntity;
}