import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

/**
 * API密钥实体
 * 存储用户的API密钥信息和配额设置
 */
@Entity('api_keys')
@Index(['key'], { unique: true })
@Index(['userId'])
export class ApiKeyEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 64, unique: true, comment: 'API密钥' })
  key: string;

  @Column({ type: 'varchar', length: 255, comment: '密钥名称' })
  name: string;

  @Column({ type: 'uuid', comment: '用户ID' })
  userId: string;

  @Column({ type: 'text', nullable: true, comment: '密钥描述' })
  description?: string;

  @Column({ type: 'boolean', default: true, comment: '是否启用' })
  enabled: boolean;

  @Column({ type: 'jsonb', nullable: true, comment: '允许的模型列表' })
  allowedModels?: string[];

  @Column({ type: 'jsonb', nullable: true, comment: '允许的IP地址列表' })
  allowedIps?: string[];

  // 配额设置
  @Column({ type: 'integer', default: 1000, comment: '每分钟请求限制' })
  rateLimit: number;

  @Column({ type: 'integer', default: 100000, comment: '每月token配额' })
  monthlyTokenQuota: number;

  @Column({ type: 'integer', default: 0, comment: '当前月已使用token数' })
  monthlyTokenUsed: number;

  @Column({ type: 'decimal', precision: 10, scale: 4, default: 0, comment: '当前月费用' })
  monthlyCost: number;

  @Column({ type: 'decimal', precision: 10, scale: 4, default: 100, comment: '月度费用限制' })
  monthlyCostLimit: number;

  // 统计信息
  @Column({ type: 'integer', default: 0, comment: '总请求次数' })
  totalRequests: number;

  @Column({ type: 'integer', default: 0, comment: '总token使用量' })
  totalTokens: number;

  @Column({ type: 'timestamp', nullable: true, comment: '最后使用时间' })
  lastUsedAt?: Date;

  @Column({ type: 'timestamp', nullable: true, comment: '过期时间' })
  expiresAt?: Date;

  @CreateDateColumn({ comment: '创建时间' })
  createdAt: Date;

  @UpdateDateColumn({ comment: '更新时间' })
  updatedAt: Date;
}