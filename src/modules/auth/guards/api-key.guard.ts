import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
  Logger,
} from '@nestjs/common';
import { Request } from 'express';
import { AuthService } from '../auth.service';
import { ApiKeyEntity } from '../entities/api-key.entity';

/**
 * API密钥认证守卫
 * 验证请求中的API密钥并进行权限检查
 */
@Injectable()
export class ApiKeyGuard implements CanActivate {
  private readonly logger = new Logger(ApiKeyGuard.name);

  constructor(private readonly authService: AuthService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<Request>();
    
    try {
      // 提取API密钥
      const apiKey = this.extractApiKey(request);
      
      if (!apiKey) {
        throw new UnauthorizedException('缺少API密钥');
      }

      // 验证API密钥
      const apiKeyEntity = await this.authService.validateApiKey(apiKey);
      
      // 检查IP访问权限
      const clientIp = this.getClientIp(request);
      await this.authService.checkIpAccess(apiKeyEntity, clientIp);

      // 将API密钥信息附加到请求对象
      request['apiKey'] = apiKeyEntity;
      request['clientIp'] = clientIp;

      return true;
    } catch (error) {
      this.logger.warn(`API密钥验证失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 从请求中提取API密钥
   */
  private extractApiKey(request: Request): string | null {
    // 从 Authorization header 提取
    const authHeader = request.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }

    // 从 X-API-Key header 提取
    const apiKeyHeader = request.headers['x-api-key'];
    if (apiKeyHeader && typeof apiKeyHeader === 'string') {
      return apiKeyHeader;
    }

    return null;
  }

  /**
   * 获取客户端IP地址
   */
  private getClientIp(request: Request): string {
    return (
      request.headers['x-forwarded-for'] as string ||
      request.headers['x-real-ip'] as string ||
      request.connection.remoteAddress ||
      request.socket.remoteAddress ||
      '127.0.0.1'
    );
  }
}

/**
 * 扩展Request接口以包含API密钥信息
 */
import 'express';

declare module 'express' {
  interface Request {
    apiKey?: ApiKeyEntity;
    clientIp?: string;
  }
}