import { Injectable, NestInterceptor, ExecutionContext, <PERSON><PERSON><PERSON><PERSON>, Logger } from '@nestjs/common';
import { Observable } from 'rxjs';
import { map, tap } from 'rxjs/operators';
import { Request, Response } from 'express';

/**
 * 标准响应接口
 */
interface StandardResponse<T> {
  success: boolean;
  data: T;
  timestamp: string;
  path: string;
}

/**
 * 统一响应格式拦截器
 * 将所有API响应格式化为统一结构
 */
@Injectable()
export class ResponseInterceptor implements NestInterceptor<unknown, StandardResponse<unknown>> {
  private readonly logger = new Logger(ResponseInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<StandardResponse<unknown>> {
    const req = context.switchToHttp().getRequest<Request>();
    const res = context.switchToHttp().getResponse<Response>();
    const startTime = Date.now();
    
    // 安全地获取请求信息
    const method = req.method || 'UNKNOWN';
    const url = req.url || 'UNKNOWN';

    return next.handle().pipe(
      tap(() => {
        const duration = Date.now() - startTime;
        const statusCode = res.statusCode;
        this.logger.log(
          `${method} ${url} - ${statusCode} - ${duration}ms`,
        );
      }),
      map((data) => {
        // 检查响应头是否为流式响应
        const contentType = res.getHeader('content-type');
        const isEventStream = typeof contentType === 'string' && contentType.includes('text/event-stream');
        
        if (isEventStream) {
          return data as unknown as StandardResponse<unknown>;
        }

        // 如果已经是标准格式，直接返回
        if (data && typeof data === 'object' && 'success' in data) {
          return data as StandardResponse<unknown>;
        }

        // 格式化为统一响应结构
        return {
          success: true,
          data,
          timestamp: new Date().toISOString(),
          path: url,
        };
      }),
    );
  }
}
