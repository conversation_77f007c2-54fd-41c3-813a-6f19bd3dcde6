import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsArray,
  IsOptional,
  IsNumber,
  IsBoolean,
  Min,
  Max,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

/**
 * 聊天消息DTO
 */
export class ChatMessageDto {
  @ApiProperty({
    description: '消息角色',
    enum: ['system', 'user', 'assistant'],
    example: 'user',
  })
  @IsString()
  role: 'system' | 'user' | 'assistant';

  @ApiProperty({
    description: '消息内容',
    example: '你好，请介绍一下自己',
  })
  @IsString()
  content: string;
}

/**
 * 聊天完成请求DTO
 */
export class ChatCompletionDto {
  @ApiProperty({
    description: '要使用的模型',
    example: 'gpt-3.5-turbo',
  })
  @IsString()
  model: string;

  @ApiProperty({
    description: '对话消息列表',
    type: [ChatMessageDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ChatMessageDto)
  messages: ChatMessageDto[];

  @ApiPropertyOptional({
    description: '生成文本的随机性，0-2之间',
    minimum: 0,
    maximum: 2,
    default: 1,
    example: 0.7,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(2)
  temperature?: number;

  @ApiPropertyOptional({
    description: '最大生成token数',
    minimum: 1,
    maximum: 4096,
    example: 1000,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(4096)
  max_tokens?: number;

  @ApiPropertyOptional({
    description: '是否启用流式响应',
    default: false,
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  stream?: boolean;

  @ApiPropertyOptional({
    description: '核采样参数，0-1之间',
    minimum: 0,
    maximum: 1,
    example: 0.9,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  top_p?: number;

  @ApiPropertyOptional({
    description: '频率惩罚，-2到2之间',
    minimum: -2,
    maximum: 2,
    example: 0,
  })
  @IsOptional()
  @IsNumber()
  @Min(-2)
  @Max(2)
  frequency_penalty?: number;

  @ApiPropertyOptional({
    description: '存在惩罚，-2到2之间',
    minimum: -2,
    maximum: 2,
    example: 0,
  })
  @IsOptional()
  @IsNumber()
  @Min(-2)
  @Max(2)
  presence_penalty?: number;

  @ApiPropertyOptional({
    description: '停止词列表',
    type: [String],
    example: ['\n', '###'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  stop?: string[];

  @ApiPropertyOptional({
    description: '用户标识符',
    example: 'user-123',
  })
  @IsOptional()
  @IsString()
  user?: string;
}

/**
 * Token使用统计DTO
 */
export class TokenUsageDto {
  @ApiProperty({
    description: '输入token数量',
    example: 50,
  })
  prompt_tokens: number;

  @ApiProperty({
    description: '输出token数量',
    example: 100,
  })
  completion_tokens: number;

  @ApiProperty({
    description: '总token数量',
    example: 150,
  })
  total_tokens: number;
}

/**
 * 聊天完成响应DTO
 */
export class ChatCompletionResponseDto {
  @ApiProperty({
    description: '响应ID',
    example: 'chatcmpl-123',
  })
  id: string;

  @ApiProperty({
    description: '对象类型',
    example: 'chat.completion',
  })
  object: string;

  @ApiProperty({
    description: '创建时间戳',
    example: 1677652288,
  })
  created: number;

  @ApiProperty({
    description: '使用的模型',
    example: 'gpt-3.5-turbo',
  })
  model: string;

  @ApiProperty({
    description: '选择列表',
    type: 'array',
  })
  choices: Array<{
    index: number;
    message: ChatMessageDto;
    finish_reason: string;
  }>;

  @ApiProperty({
    description: 'Token使用统计',
    type: TokenUsageDto,
  })
  usage: TokenUsageDto;
}