import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { Request } from 'express';

// 扩展Express的Request类型，添加user属性
interface RequestWithUser extends Request {
  user?: {
    id?: string | number;
  };
}

/**
 * 用户ID装饰器
 * 从请求上下文中提取用户ID
 * 
 * 注意：需要确保请求对象上已经通过认证中间件添加了user属性
 */
export const UserId = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): string | undefined => {
    const request = ctx.switchToHttp().getRequest<RequestWithUser>();
    
    // 使用类型安全的方式访问user和id属性
    if (request.user && request.user.id !== undefined) {
      return String(request.user.id);
    }
    
    return undefined;
  },
);
