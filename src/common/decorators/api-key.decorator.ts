import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { Request } from 'express';

/**
 * API密钥装饰器
 * 从请求头中提取API密钥
 */
export const ApiKey = createParamDecorator(
  (_: never, ctx: ExecutionContext): string | undefined => {
    // 获取请求对象
    const request = ctx.switchToHttp().getRequest<Request>();
    
    // 安全地获取API密钥
    let apiKey: string | undefined;
    const xApiKey = request.header('x-api-key');
    if (xApiKey) {
      apiKey = xApiKey;
    }
    
    // 如果没有API密钥，尝试从Authorization头中获取
    if (!apiKey) {
      const authHeader = request.header('authorization');
      if (authHeader && authHeader.startsWith('Bearer ')) {
        apiKey = authHeader.substring(7); // 移除 'Bearer ' 前缀
      }
    }
    
    return apiKey;
  },
);
