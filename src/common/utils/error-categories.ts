/**
 * 错误类型枚举
 */
export enum ErrorType {
  NETWORK = 'NETWORK',
  TIMEOUT = 'TIMEOUT',
  AUTHENTICATION = 'AUTHENTICATION',
  RATE_LIMIT = 'RATE_LIMIT',
  INVALID_REQUEST = 'INVALID_REQUEST',
  SERVER_ERROR = 'SERVER_ERROR',
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',
  UNKNOWN = 'UNKNOWN',
}

/**
 * 提供商错误类
 */
export class ProviderError extends Error {
  constructor(
    public readonly type: ErrorType,
    public readonly provider: string,
    public readonly originalError?: Error,
    public readonly statusCode?: number,
  ) {
    super(`${provider}: ${originalError?.message || 'Unknown error'}`);
    this.name = 'ProviderError';
  }

  /**
   * 判断是否为可重试错误
   */
  isRetryable(): boolean {
    return [
      ErrorType.NETWORK,
      ErrorType.TIMEOUT,
      ErrorType.SERVER_ERROR,
      ErrorType.SERVICE_UNAVAILABLE,
    ].includes(this.type);
  }

  /**
   * 判断是否为熔断器触发错误
   */
  isCircuitBreakerTrigger(): boolean {
    return [
      ErrorType.NETWORK,
      ErrorType.TIMEOUT,
      ErrorType.SERVER_ERROR,
      ErrorType.SERVICE_UNAVAILABLE,
    ].includes(this.type);
  }
}

/**
 * 错误分类工具
 */
export class ErrorClassifier {
  /**
   * 根据错误信息分类错误类型
   */
  static classify(error: any, provider: string): ProviderError {
    if (error instanceof ProviderError) {
      return error;
    }

    const message = error.message || String(error);
    const statusCode = error.response?.status || error.status;

    // 网络错误
    if (message.includes('ENOTFOUND') || 
        message.includes('ECONNREFUSED') || 
        message.includes('network') ||
        message.includes('Network')) {
      return new ProviderError(ErrorType.NETWORK, provider, error);
    }

    // 超时错误
    if (message.includes('timeout') || 
        message.includes('Timeout') || 
        error.code === 'ECONNABORTED') {
      return new ProviderError(ErrorType.TIMEOUT, provider, error);
    }

    // 认证错误
    if (statusCode === 401 || 
        message.includes('authentication') || 
        message.includes('unauthorized') ||
        message.includes('API key')) {
      return new ProviderError(ErrorType.AUTHENTICATION, provider, error, 401);
    }

    // 限流错误
    if (statusCode === 429 || 
        message.includes('rate limit') || 
        message.includes('quota')) {
      return new ProviderError(ErrorType.RATE_LIMIT, provider, error, 429);
    }

    // 无效请求
    if (statusCode === 400 || 
        statusCode === 422 || 
        message.includes('invalid') || 
        message.includes('bad request')) {
      return new ProviderError(ErrorType.INVALID_REQUEST, provider, error, statusCode);
    }

    // 服务器错误
    if (statusCode >= 500 && statusCode < 600) {
      return new ProviderError(ErrorType.SERVER_ERROR, provider, error, statusCode);
    }

    // 服务不可用
    if (statusCode === 503 || 
        message.includes('service unavailable') || 
        message.includes('maintenance')) {
      return new ProviderError(ErrorType.SERVICE_UNAVAILABLE, provider, error, 503);
    }

    // 未知错误
    return new ProviderError(ErrorType.UNKNOWN, provider, error, statusCode);
  }
}