/**
 * 熔断器状态枚举
 */
export enum CircuitBreakerState {
  CLOSED = 'CLOSED',
  OPEN = 'OPEN',
  HALF_OPEN = 'HALF_OPEN',
}

/**
 * 熔断器配置接口
 */
export interface CircuitBreakerConfig {
  failureThreshold: number;      // 失败阈值
  resetTimeout: number;          // 重置超时时间（毫秒）
  monitoringPeriod: number;      // 监控周期（毫秒）
  halfOpenMaxCalls: number;      // 半开状态下最大调用次数
}

/**
 * 熔断器实现
 */
export class CircuitBreaker {
  private state: CircuitBreakerState = CircuitBreakerState.CLOSED;
  private failureCount = 0;
  private lastFailureTime: number | null = null;
  private successCount = 0;
  private nextAttempt: number = Date.now();

  constructor(
    private readonly name: string,
    private readonly config: CircuitBreakerConfig,
  ) {}

  /**
   * 执行操作，如果熔断器关闭则允许执行
   */
  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === CircuitBreakerState.OPEN) {
      if (Date.now() < this.nextAttempt) {
        throw new Error(`${this.name} 熔断器已打开，请稍后重试`);
      }
      this.state = CircuitBreakerState.HALF_OPEN;
      this.successCount = 0;
    }

    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }

  /**
   * 处理成功
   */
  private onSuccess(): void {
    this.failureCount = 0;

    if (this.state === CircuitBreakerState.HALF_OPEN) {
      this.successCount++;
      if (this.successCount >= this.config.halfOpenMaxCalls) {
        this.state = CircuitBreakerState.CLOSED;
      }
    }
  }

  /**
   * 处理失败
   */
  private onFailure(): void {
    this.failureCount++;
    this.lastFailureTime = Date.now();

    if (this.failureCount >= this.config.failureThreshold) {
      this.state = CircuitBreakerState.OPEN;
      this.nextAttempt = Date.now() + this.config.resetTimeout;
    }
  }

  /**
   * 获取当前状态
   */
  getState(): CircuitBreakerState {
    return this.state;
  }

  /**
   * 获取失败次数
   */
  getFailureCount(): number {
    return this.failureCount;
  }

  /**
   * 手动重置熔断器
   */
  reset(): void {
    this.state = CircuitBreakerState.CLOSED;
    this.failureCount = 0;
    this.lastFailureTime = null;
    this.successCount = 0;
  }

  /**
   * 获取熔断器状态信息
   */
  getStatus() {
    return {
      name: this.name,
      state: this.state,
      failureCount: this.failureCount,
      lastFailureTime: this.lastFailureTime,
      nextAttempt: this.state === CircuitBreakerState.OPEN ? this.nextAttempt : null,
    };
  }
}

/**
 * 熔断器管理器
 */
export class CircuitBreakerManager {
  private readonly breakers = new Map<string, CircuitBreaker>();

  /**
   * 获取或创建熔断器
   */
  getBreaker(name: string, config?: CircuitBreakerConfig): CircuitBreaker {
    if (!this.breakers.has(name)) {
      const defaultConfig: CircuitBreakerConfig = {
        failureThreshold: 5,
        resetTimeout: 60000, // 1分钟
        monitoringPeriod: 60000,
        halfOpenMaxCalls: 3,
      };
      
      this.breakers.set(name, new CircuitBreaker(name, config || defaultConfig));
    }
    
    return this.breakers.get(name)!;
  }

  /**
   * 获取所有熔断器状态
   */
  getAllStatus() {
    const status: Record<string, any> = {};
    for (const [name, breaker] of this.breakers) {
      status[name] = breaker.getStatus();
    }
    return status;
  }

  /**
   * 重置指定熔断器
   */
  resetBreaker(name: string): boolean {
    const breaker = this.breakers.get(name);
    if (breaker) {
      breaker.reset();
      return true;
    }
    return false;
  }
}