import { Observable } from 'rxjs';
import { ChatCompletionDto, ChatCompletionResponseDto } from '../dto/chat-completion.dto';

/**
 * 模型提供商基础接口
 * 定义所有LLM提供商必须实现的标准方法
 */
export interface ILLMProvider {
  /**
   * 提供商名称
   */
  readonly name: string;

  /**
   * 支持的模型列表
   */
  readonly supportedModels: string[];

  /**
   * 聊天完成接口
   * @param request 聊天完成请求
   * @returns 聊天完成响应或流式响应
   */
  chatCompletion(
    request: ChatCompletionDto,
  ): Promise<ChatCompletionResponseDto> | Observable<string>;

  /**
   * 检查模型是否支持
   * @param model 模型名称
   * @returns 是否支持该模型
   */
  isModelSupported(model: string): boolean;

  /**
   * 获取模型列表
   * @returns 支持的模型列表
   */
  getModels(): Promise<string[]>;

  /**
   * 健康检查
   * @returns 提供商是否可用
   */
  healthCheck(): Promise<boolean>;
}

/**
 * 提供商配置接口
 */
export interface IProviderConfig {
  apiKey?: string;
  baseUrl: string;
  timeout: number;
  maxRetries: number;
  models: string[];
  circuitBreaker?: {
    failureThreshold: number;
    resetTimeout: number;
    monitoringPeriod: number;
    halfOpenMaxCalls: number;
  };
  healthCheckTimeout?: number;
}

/**
 * 路由策略枚举
 */
export enum RoutingStrategy {
  ROUND_ROBIN = 'round_robin',
  WEIGHTED_ROUND_ROBIN = 'weighted_round_robin',
  LEAST_CONNECTIONS = 'least_connections',
  RANDOM = 'random',
}

/**
 * 负载均衡配置接口
 */
export interface ILoadBalancerConfig {
  strategy: RoutingStrategy;
  weights?: Record<string, number>;
  healthCheckInterval: number;
  failoverEnabled: boolean;
  maxRetries: number;
  retryDelay: number;
}