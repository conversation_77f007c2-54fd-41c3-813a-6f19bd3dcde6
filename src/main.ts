import { NestFactory } from '@nestjs/core';
import { ValidationPipe, Logger } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { ConfigService } from '@nestjs/config';
import helmet from 'helmet';
import compression from 'compression';
import { AppModule } from './modules/app/app.module';

/**
 * 应用程序启动函数
 * 配置全局中间件、验证管道、Swagger文档等
 */
async function bootstrap() {
  const logger = new Logger('Bootstrap');
  
  // 创建NestJS应用实例
  const app = await NestFactory.create(AppModule, {
    logger: ['error', 'warn', 'log', 'debug', 'verbose'],
  });

  // 获取配置服务
  const configService = app.get(ConfigService);
  const port = configService.get<number>('PORT', 3000);
  const apiPrefix = configService.get<string>('API_PREFIX', 'v1');

  // 启用CORS
  app.enableCors({
    origin: true,
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
    credentials: true,
  });

  // 安全中间件
  app.use(helmet());
  
  // 启用压缩
  app.use(compression());

  // 设置全局API前缀
  app.setGlobalPrefix(apiPrefix);

  // 全局验证管道
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }),
  );

  // 配置Swagger文档
  const config = new DocumentBuilder()
    .setTitle('LLM API Gateway')
    .setDescription('高性能多模型LLM API网关 - 统一接口，智能路由，高可用性')
    .setVersion('1.0.0')
    .addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        name: 'Authorization',
        description: 'Enter JWT token',
        in: 'header',
      },
      'JWT-auth',
    )
    .addApiKey(
      {
        type: 'apiKey',
        name: 'X-API-Key',
        in: 'header',
        description: 'API密钥认证',
      },
      'api-key',
    )
    .addTag('Chat', '聊天完成接口')
    .addTag('Auth', '认证管理')
    .addTag('Metrics', '监控指标')
    .addTag('Health', '健康检查')
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api', app, document, {
    swaggerOptions: {
      persistAuthorization: true,
    },
  });

  // 启动应用
  await app.listen(port);
  
  logger.log(`🚀 应用已启动在端口 ${port}`);
  logger.log(`📚 API文档地址: http://localhost:${port}/api`);
  logger.log(`🔍 健康检查: http://localhost:${port}/${apiPrefix}/health`);
  logger.log(`📊 监控指标: http://localhost:${port}/metrics`);
}

// 启动应用并处理未捕获的异常
bootstrap().catch((error) => {
  const logger = new Logger('Bootstrap');
  logger.error('应用启动失败:', error);
  process.exit(1);
});