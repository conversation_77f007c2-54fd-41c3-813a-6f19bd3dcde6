import { registerAs } from '@nestjs/config';

/**
 * LLM提供商配置
 * 包含各个模型提供商的API配置信息
 */
export default registerAs('providers', () => ({
  // OpenAI配置
  openai: {
    apiKey: process.env.OPENAI_API_KEY,
    baseUrl: process.env.OPENAI_BASE_URL || 'https://api.openai.com/v1',
    timeout: parseInt(process.env.OPENAI_TIMEOUT || '120000', 10),
    maxRetries: parseInt(process.env.OPENAI_MAX_RETRIES || '3', 10),
    healthCheckTimeout: parseInt(process.env.OPENAI_HEALTH_CHECK_TIMEOUT || '5000', 10),
    models: [
      'gpt-4',
      'gpt-4-turbo',
      'gpt-3.5-turbo',
      'gpt-3.5-turbo-16k',
    ],
    circuitBreaker: {
      failureThreshold: parseInt(process.env.OPENAI_CIRCUIT_BREAKER_FAILURE_THRESHOLD || '5', 10),
      resetTimeout: parseInt(process.env.OPENAI_CIRCUIT_BREAKER_RESET_TIMEOUT || '60000', 10),
      monitoringPeriod: parseInt(process.env.OPENAI_CIRCUIT_BREAKER_MONITORING_PERIOD || '60000', 10),
      halfOpenMaxCalls: parseInt(process.env.OPENAI_CIRCUIT_BREAKER_HALF_OPEN_MAX_CALLS || '3', 10),
    },
  },
  
  // Anthropic配置
  anthropic: {
    apiKey: process.env.ANTHROPIC_API_KEY,
    baseUrl: process.env.ANTHROPIC_BASE_URL || 'https://api.anthropic.com',
    timeout: parseInt(process.env.ANTHROPIC_TIMEOUT || '120000', 10),
    maxRetries: parseInt(process.env.ANTHROPIC_MAX_RETRIES || '3', 10),
    healthCheckTimeout: parseInt(process.env.ANTHROPIC_HEALTH_CHECK_TIMEOUT || '5000', 10),
    models: [
      'claude-3-opus-20240229',
      'claude-3-sonnet-20240229',
      'claude-3-haiku-20240307',
      'claude-2.1',
    ],
    circuitBreaker: {
      failureThreshold: parseInt(process.env.ANTHROPIC_CIRCUIT_BREAKER_FAILURE_THRESHOLD || '5', 10),
      resetTimeout: parseInt(process.env.ANTHROPIC_CIRCUIT_BREAKER_RESET_TIMEOUT || '60000', 10),
      monitoringPeriod: parseInt(process.env.ANTHROPIC_CIRCUIT_BREAKER_MONITORING_PERIOD || '60000', 10),
      halfOpenMaxCalls: parseInt(process.env.ANTHROPIC_CIRCUIT_BREAKER_HALF_OPEN_MAX_CALLS || '3', 10),
    },
  },
  
  // Ollama配置
  ollama: {
    baseUrl: process.env.OLLAMA_BASE_URL || 'http://localhost:11434',
    timeout: parseInt(process.env.OLLAMA_TIMEOUT || '120000', 10),
    maxRetries: parseInt(process.env.OLLAMA_MAX_RETRIES || '2', 10),
    healthCheckTimeout: parseInt(process.env.OLLAMA_HEALTH_CHECK_TIMEOUT || '3000', 10),
    models: [
      'llama2',
      'codellama',
      'mistral',
      'neural-chat',
    ],
    circuitBreaker: {
      failureThreshold: parseInt(process.env.OLLAMA_CIRCUIT_BREAKER_FAILURE_THRESHOLD || '3', 10),
      resetTimeout: parseInt(process.env.OLLAMA_CIRCUIT_BREAKER_RESET_TIMEOUT || '30000', 10),
      monitoringPeriod: parseInt(process.env.OLLAMA_CIRCUIT_BREAKER_MONITORING_PERIOD || '30000', 10),
      halfOpenMaxCalls: parseInt(process.env.OLLAMA_CIRCUIT_BREAKER_HALF_OPEN_MAX_CALLS || '2', 10),
    },
  },
  
  // 自定义端点配置
  custom: {
    baseUrl: process.env.CUSTOM_ENDPOINT_URL,
    apiKey: process.env.CUSTOM_ENDPOINT_API_KEY,
    timeout: parseInt(process.env.CUSTOM_TIMEOUT || '60000', 10),
    maxRetries: parseInt(process.env.CUSTOM_MAX_RETRIES || '3', 10),
    healthCheckTimeout: parseInt(process.env.CUSTOM_HEALTH_CHECK_TIMEOUT || '5000', 10),
    models: [], // 动态获取
    circuitBreaker: {
      failureThreshold: parseInt(process.env.CUSTOM_CIRCUIT_BREAKER_FAILURE_THRESHOLD || '5', 10),
      resetTimeout: parseInt(process.env.CUSTOM_CIRCUIT_BREAKER_RESET_TIMEOUT || '60000', 10),
      monitoringPeriod: parseInt(process.env.CUSTOM_CIRCUIT_BREAKER_MONITORING_PERIOD || '60000', 10),
      halfOpenMaxCalls: parseInt(process.env.CUSTOM_CIRCUIT_BREAKER_HALF_OPEN_MAX_CALLS || '3', 10),
    },
  },
}));