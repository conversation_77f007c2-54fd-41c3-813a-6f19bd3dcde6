import { Test } from '@nestjs/testing';
import { ConfigModule } from '@nestjs/config';
import databaseConfig from './database.config';
import { PostgresConnectionOptions } from 'typeorm/driver/postgres/PostgresConnectionOptions';

describe('DatabaseConfig', () => {
  // 保存原始环境变量
  const originalEnv = { ...process.env };

  // 在每个测试前重置环境变量
  beforeEach(() => {
    jest.resetModules();
    process.env = { ...originalEnv };

    // 清除测试相关的环境变量
    delete process.env.DB_HOST;
    delete process.env.DB_PORT;
    delete process.env.DB_USERNAME;
    delete process.env.DB_PASSWORD;
    delete process.env.DB_DATABASE;
    delete process.env.NODE_ENV;
  });

  // 在所有测试后恢复环境变量
  afterAll(() => {
    process.env = originalEnv;
  });

  describe('基本配置', () => {
    let config: PostgresConnectionOptions;

    beforeEach(() => {
      // 正确获取配置对象，databaseConfig() 返回配置对象
      config = databaseConfig();
    });

    it('应返回PostgreSQL数据库类型', () => {
      expect(config.type).toBe('postgres');
    });

    it('应使用默认连接参数', () => {
      expect(config.host).toBe('localhost');
      expect(config.port).toBe(5432);
      expect(config.username).toBe('postgres');
      expect(config.password).toBe('password');
      expect(config.database).toBe('llm_gateway');
    });

    it('应正确配置实体路径', () => {
      expect(config.entities).toContain(__dirname + '/../**/*.entity{.ts,.js}');
    });

    it('应正确配置迁移设置', () => {
      expect(config.migrations).toContain(__dirname + '/../migrations/*{.ts,.js}');
      expect(config.migrationsTableName).toBe('migrations');
    });
  });

  describe('环境变量覆盖', () => {
    it('应使用环境变量覆盖默认连接参数', () => {
      // 设置环境变量
      process.env.DB_HOST = 'custom-host';
      process.env.DB_PORT = '9999';
      process.env.DB_USERNAME = 'custom-user';
      process.env.DB_PASSWORD = 'custom-password';
      process.env.DB_DATABASE = 'custom-db';

      const config = databaseConfig();

      // 验证配置
      expect(config.host).toBe('custom-host');
      expect(config.port).toBe(9999);
      expect(config.username).toBe('custom-user');
      expect(config.password).toBe('custom-password');
      expect(config.database).toBe('custom-db');
    });

    it('应处理无效的端口号', () => {
      process.env.DB_PORT = 'not-a-number';
      const config = databaseConfig();
      expect(config.port).toBe(5432); // 应回退到默认值
    });
  });

  describe('环境特定配置', () => {
    it('开发环境应启用同步和详细日志', () => {
      process.env.NODE_ENV = 'development';
      const config = databaseConfig();

      expect(config.synchronize).toBe(true);
      expect(config.logging).toEqual(['query', 'error']);
    });

    it('生产环境应禁用同步并只记录错误', () => {
      process.env.NODE_ENV = 'production';
      const config = databaseConfig();

      expect(config.synchronize).toBe(false);
      expect(config.logging).toEqual(['error']);
    });

    it('测试环境应禁用同步并只记录错误', () => {
      process.env.NODE_ENV = 'test';
      const config = databaseConfig();

      expect(config.synchronize).toBe(false);
      expect(config.logging).toEqual(['error']);
    });
  });

  describe('SSL配置', () => {
    it('生产环境应启用SSL但不验证证书', () => {
      process.env.NODE_ENV = 'production';
      const config = databaseConfig();

      expect(config.ssl).toEqual({ rejectUnauthorized: false });
    });

    it('非生产环境应禁用SSL', () => {
      process.env.NODE_ENV = 'development';
      const devConfig = databaseConfig();
      expect(devConfig.ssl).toBe(false);

      process.env.NODE_ENV = 'test';
      const testConfig = databaseConfig();
      expect(testConfig.ssl).toBe(false);
    });
  });

  describe('连接池配置', () => {
    it('应包含正确的连接池设置', () => {
      const config = databaseConfig();

      expect(config.extra).toBeDefined();
      expect(config.extra).toEqual({
        max: 20,
        min: 5,
        acquire: 30000,
        idle: 10000,
      });
    });
  });

  describe('与NestJS集成', () => {
    it('应能通过ConfigModule正确加载', async () => {
      const moduleRef = await Test.createTestingModule({
        imports: [
          ConfigModule.forRoot({
            load: [databaseConfig],
          }),
        ],
      }).compile();

      expect(moduleRef).toBeDefined();
    });
  });
});
