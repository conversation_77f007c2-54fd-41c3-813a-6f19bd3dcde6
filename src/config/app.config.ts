import { registerAs } from '@nestjs/config';

/**
 * 应用配置
 * 包含基础应用设置、端口、环境等信息
 */
export default registerAs('app', () => ({
  // 应用基础配置
  nodeEnv: process.env.NODE_ENV || 'development',
  port: parseInt(process.env.PORT, 10) || 3000,
  apiPrefix: process.env.API_PREFIX || 'v1',
  
  // JWT配置
  jwt: {
    secret: process.env.JWT_SECRET || 'your-super-secret-jwt-key',
    expiresIn: process.env.JWT_EXPIRES_IN || '7d',
  },
  
  // 缓存配置
  cache: {
    ttl: parseInt(process.env.CACHE_TTL, 10) || 3600,
    enabled: process.env.ENABLE_CACHE === 'true',
  },
  
  // 速率限制配置
  rateLimit: {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS, 10) || 60000,
    maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS, 10) || 100,
  },
  
  // 监控配置
  metrics: {
    enabled: process.env.ENABLE_METRICS === 'true',
    port: parseInt(process.env.METRICS_PORT, 10) || 9090,
  },
  
  // 日志配置
  log: {
    level: process.env.LOG_LEVEL || 'info',
  },
}));