import { registerAs } from '@nestjs/config';

/**
 * Redis配置
 * 用于缓存和速率限制
 */
export default registerAs('redis', () => ({
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379', 10),
  password: process.env.REDIS_PASSWORD || undefined,
  
  // 连接配置
  connectTimeout: 10000,
  lazyConnect: true,
  maxRetriesPerRequest: 3,
  retryDelayOnFailover: 100,
  
  // 连接池配置
  family: 4,
  keepAlive: true,
  
  // 键前缀
  keyPrefix: 'llm_gateway:',
  
  // 数据库索引
  db: 0,
}));