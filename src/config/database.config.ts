import { registerAs } from '@nestjs/config';
import { DataSourceOptions } from 'typeorm';

/**
 * 数据库配置
 * PostgreSQL连接配置和TypeORM设置
 */
export default registerAs('database', (): DataSourceOptions => ({
  type: 'postgres',
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT, 10) || 5432,
  username: process.env.DB_USERNAME || 'postgres',
  password: process.env.DB_PASSWORD || 'password',
  database: process.env.DB_DATABASE || 'llm_gateway',
  
  // 迁移配置
  migrations: ['src/migrations/*.{ts,js}'],
  migrationsTableName: 'migrations',
  
  // 开发环境配置
  synchronize: process.env.NODE_ENV === 'development',
  logging: process.env.NODE_ENV === 'development' ? ['query', 'error'] : ['error'],
  
  // 连接池配置
  extra: {
    max: 20, // 最大连接数
    min: 5,  // 最小连接数
    acquire: 30000, // 获取连接超时时间
    idle: 10000,    // 空闲连接超时时间
  },
  
  // SSL配置（生产环境）
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
}));
