# 应用配置
NODE_ENV=development
PORT=3000
API_PREFIX=v1

# 数据库配置 (PostgreSQL)
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=password
DB_DATABASE=llm_gateway

# Redis 配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# JWT 配置
JWT_SECRET=development-jwt-secret-key-for-testing
JWT_EXPIRES_IN=7d

# OpenAI 配置
OPENAI_API_KEY=sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_TIMEOUT=120000
OPENAI_MAX_RETRIES=3
OPENAI_HEALTH_CHECK_TIMEOUT=5000

# OpenAI 熔断器配置
OPENAI_CIRCUIT_BREAKER_FAILURE_THRESHOLD=5
OPENAI_CIRCUIT_BREAKER_RESET_TIMEOUT=60000
OPENAI_CIRCUIT_BREAKER_MONITORING_PERIOD=60000
OPENAI_CIRCUIT_BREAKER_HALF_OPEN_MAX_CALLS=3

# Anthropic 配置
ANTHROPIC_API_KEY=sk-ant-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
ANTHROPIC_BASE_URL=https://api.anthropic.com
ANTHROPIC_TIMEOUT=120000
ANTHROPIC_MAX_RETRIES=3
ANTHROPIC_HEALTH_CHECK_TIMEOUT=5000

# Anthropic 熔断器配置
ANTHROPIC_CIRCUIT_BREAKER_FAILURE_THRESHOLD=5
ANTHROPIC_CIRCUIT_BREAKER_RESET_TIMEOUT=60000
ANTHROPIC_CIRCUIT_BREAKER_MONITORING_PERIOD=60000
ANTHROPIC_CIRCUIT_BREAKER_HALF_OPEN_MAX_CALLS=3

# Ollama 配置
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_TIMEOUT=120000
OLLAMA_MAX_RETRIES=2
OLLAMA_HEALTH_CHECK_TIMEOUT=3000

# Ollama 熔断器配置
OLLAMA_CIRCUIT_BREAKER_FAILURE_THRESHOLD=3
OLLAMA_CIRCUIT_BREAKER_RESET_TIMEOUT=30000
OLLAMA_CIRCUIT_BREAKER_MONITORING_PERIOD=30000
OLLAMA_CIRCUIT_BREAKER_HALF_OPEN_MAX_CALLS=2

# 自定义端点配置
# CUSTOM_ENDPOINT_URL=https://api.openai.com/v1
# CUSTOM_ENDPOINT_API_KEY=your-custom-api-key
CUSTOM_TIMEOUT=120000
CUSTOM_MAX_RETRIES=3
CUSTOM_HEALTH_CHECK_TIMEOUT=5000

# 自定义端点熔断器配置
CUSTOM_CIRCUIT_BREAKER_FAILURE_THRESHOLD=5
CUSTOM_CIRCUIT_BREAKER_RESET_TIMEOUT=60000
CUSTOM_CIRCUIT_BREAKER_MONITORING_PERIOD=60000
CUSTOM_CIRCUIT_BREAKER_HALF_OPEN_MAX_CALLS=3

# 缓存配置
CACHE_TTL=3600
ENABLE_CACHE=true

# 速率限制配置
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=100

# 监控配置
ENABLE_METRICS=true
METRICS_PORT=9090

# 日志配置
LOG_LEVEL=info