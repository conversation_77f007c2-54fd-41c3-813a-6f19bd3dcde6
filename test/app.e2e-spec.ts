import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import supertest from 'supertest';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AppModule } from '../src/modules/app/app.module';
import { ApiKeyEntity } from '../src/modules/auth/entities/api-key.entity';
import { UsageLogEntity } from '../src/modules/auth/entities/usage-log.entity';
import { Repository } from 'typeorm';
import { getRepositoryToken } from '@nestjs/typeorm';

// 加载测试环境变量
process.env.NODE_ENV = 'test';
process.env.DATABASE_TYPE = 'sqlite';
process.env.DATABASE_DATABASE = ':memory:';
process.env.REDIS_ENABLED = 'false';
process.env.OPENAI_API_KEY = 'test-key';
process.env.ANTHROPIC_API_KEY = 'test-key';

/**
 * 端到端测试
 * 测试完整的API流程
 */
describe('LLM API Gateway (e2e)', () => {
  let app: INestApplication;
  let agent: supertest.SuperTest<supertest.Test>;
  let apiKeyRepository: Repository<ApiKeyEntity>;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          ignoreEnvFile: true,
          load: [() => ({
            database: {
              type: 'sqlite',
              database: ':memory:',
              synchronize: true,
              dropSchema: true,
              entities: [ApiKeyEntity, UsageLogEntity],
            },
            redis: {
              enabled: false,
            },
            providers: {
              openai: {
                apiKey: 'test-key',
                baseUrl: 'https://api.openai.com/v1',
              },
              anthropic: {
                apiKey: 'test-key',
                baseUrl: 'https://api.anthropic.com/v1',
              },
              ollama: {
                baseUrl: 'http://localhost:11434',
              },
              custom: {
                endpoints: [],
              },
            },
          })],
        }),
        TypeOrmModule.forRoot({
          type: 'sqlite',
          database: ':memory:',
          synchronize: true,
          dropSchema: true,
          entities: [ApiKeyEntity, UsageLogEntity],
        }),
        AppModule,
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
    agent = supertest(app.getHttpServer());
    apiKeyRepository = moduleFixture.get<Repository<ApiKeyEntity>>(getRepositoryToken(ApiKeyEntity));
  }, 30000);

  afterAll(async () => {
    if (app) {
      await app.close();
    }
  });

  describe('健康检查', () => {
    it('/v1/health (GET)', async () => {
      const response = await agent
        .get('/v1/health')
        .expect(200);
      
      expect(response.body).toHaveProperty('status');
      expect(response.body).toHaveProperty('info');
      expect(response.body).toHaveProperty('details');
    }, 10000);

    it('/v1/health/detailed (GET)', async () => {
      const response = await agent
        .get('/v1/health/detailed')
        .expect(200);
      
      expect(response.body).toHaveProperty('timestamp');
      expect(response.body).toHaveProperty('status');
      expect(response.body).toHaveProperty('services');
      expect(response.body).toHaveProperty('system');
    }, 10000);
  });

  describe('模型列表', () => {
    it('/v1/models (GET)', async () => {
      const response = await agent
        .get('/v1/models')
        .expect(200);
      
      expect(response.body).toHaveProperty('object', 'list');
      expect(response.body).toHaveProperty('data');
      expect(Array.isArray(response.body.data)).toBe(true);
    }, 10000);
  });

  describe('监控指标', () => {
    it('/metrics (GET)', async () => {
      await agent
        .get('/metrics')
        .expect(200)
        .expect('Content-Type', /text\/plain/);
    }, 10000);

    it('/metrics/summary (GET)', async () => {
      const response = await agent
        .get('/metrics/summary')
        .expect(200);
      
      expect(response.body).toHaveProperty('httpRequests');
      expect(response.body).toHaveProperty('llmRequests');
      expect(response.body).toHaveProperty('totalTokens');
      expect(response.body).toHaveProperty('totalCost');
    }, 10000);
  });

  describe('聊天完成 (需要API密钥)', () => {
    let validApiKey: string;

    beforeAll(async () => {
      // 创建一个测试用的API密钥
      const apiKey = apiKeyRepository.create({
        key: 'test-api-key-12345678',
        name: 'Test Key',
        userId: 'test-user-id',
        rateLimit: 1000,
        monthlyTokenQuota: 1000000,
        monthlyTokenUsed: 0,
        monthlyCost: 0,
        monthlyCostLimit: 100,
        totalRequests: 0,
        totalTokens: 0,
        enabled: true,
        allowedModels: ['gpt-3.5-turbo', 'gpt-4'],
      });
      await apiKeyRepository.save(apiKey);
      validApiKey = apiKey.key;
    });

    it('/v1/chat/completions (POST) - 缺少API密钥', async () => {
      await agent
        .post('/v1/chat/completions')
        .send({
          model: 'gpt-3.5-turbo',
          messages: [{ role: 'user', content: '你好' }],
        })
        .expect(401);
    }, 10000);

    it('/v1/chat/completions (POST) - 无效的API密钥', async () => {
      await agent
        .post('/v1/chat/completions')
        .set('X-API-Key', 'invalid-key')
        .send({
          model: 'gpt-3.5-turbo',
          messages: [{ role: 'user', content: '你好' }],
        })
        .expect(401);
    }, 10000);

    it('/v1/chat/completions (POST) - 请求参数验证', async () => {
      await agent
        .post('/v1/chat/completions')
        .set('X-API-Key', validApiKey)
        .send({
          // 缺少必需的model和messages字段
        })
        .expect(400);
    }, 10000);

    it('/v1/chat/completions (POST) - 有效请求格式', async () => {
      const response = await agent
        .post('/v1/chat/completions')
        .set('X-API-Key', validApiKey)
        .send({
          model: 'gpt-3.5-turbo',
          messages: [{ role: 'user', content: '你好' }],
          max_tokens: 10,
          temperature: 0.7,
        });
      
      // 由于使用测试环境，可能返回不同的状态码
      expect([200, 400, 401, 500]).toContain(response.status);
    }, 10000);
  });
});
