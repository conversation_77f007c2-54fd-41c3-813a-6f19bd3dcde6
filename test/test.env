# 测试环境配置
NODE_ENV=test
PORT=3001

# 数据库配置 - 使用SQLite内存数据库进行测试
DATABASE_TYPE=sqlite
DATABASE_DATABASE=:memory:

# Redis配置 - 测试时禁用
REDIS_ENABLED=false
REDIS_HOST=localhost
REDIS_PORT=6379

# API配置
API_PREFIX=v1

# 提供商配置 - 测试时使用mock
OPENAI_API_KEY=test-key
OPENAI_BASE_URL=https://api.openai.com/v1
ANTHROPIC_API_KEY=test-key
ANTHROPIC_BASE_URL=https://api.anthropic.com/v1
OLLAMA_BASE_URL=http://localhost:11434
CUSTOM_ENDPOINTS=[]

# 日志配置
LOG_LEVEL=error