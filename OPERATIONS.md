# 运维操作手册

本文档提供LLM API网关的详细运维操作指南，包括中间件管理、服务监控、故障排除等。

## 🗄️ 数据库操作 (PostgreSQL)

### 连接数据库

**Docker环境:**
```bash
# 连接到Docker中的PostgreSQL
docker-compose exec postgres psql -U postgres -d llm_gateway

# 或者从宿主机连接
psql -h localhost -p 5432 -U postgres -d llm_gateway
```

**本地环境:**
```bash
# 直接连接本地PostgreSQL
psql -U postgres -d llm_gateway
```

### 常用数据库命令

```sql
-- 查看所有表
\dt

-- 查看表结构
\d api_keys
\d usage_logs

-- 查看数据库大小
SELECT pg_size_pretty(pg_database_size('llm_gateway'));

-- 查看表大小
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- 查看活跃连接
SELECT 
    pid,
    usename,
    application_name,
    client_addr,
    state,
    query_start,
    query
FROM pg_stat_activity 
WHERE datname = 'llm_gateway';

-- 查看慢查询
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    rows
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;
```

### 数据维护

```sql
-- 清理过期数据
SELECT cleanup_expired_data();

-- 手动清理30天前的使用日志
DELETE FROM usage_logs WHERE created_at < NOW() - INTERVAL '30 days';

-- 重建索引
REINDEX TABLE usage_logs;

-- 更新表统计信息
ANALYZE api_keys;
ANALYZE usage_logs;

-- 查看API密钥使用统计
SELECT * FROM api_key_usage_stats ORDER BY recent_requests DESC;

-- 查看模型使用统计
SELECT * FROM model_usage_stats WHERE date >= CURRENT_DATE - INTERVAL '7 days';
```

### 备份和恢复

```bash
# 备份数据库
docker-compose exec postgres pg_dump -U postgres llm_gateway > backup_$(date +%Y%m%d_%H%M%S).sql

# 恢复数据库
docker-compose exec -T postgres psql -U postgres llm_gateway < backup_20240101_120000.sql

# 备份到Docker卷
docker-compose exec postgres pg_dump -U postgres -f /var/lib/postgresql/data/backup.sql llm_gateway
```

## 🔴 Redis操作

### 连接Redis

**Docker环境:**
```bash
# 连接到Docker中的Redis
docker-compose exec redis redis-cli

# 从宿主机连接
redis-cli -h localhost -p 6379
```

**本地环境:**
```bash
# 直接连接本地Redis
redis-cli
```

### 常用Redis命令

```bash
# 测试连接
PING

# 查看所有键
KEYS *

# 查看特定模式的键
KEYS "llm_gateway:*"
KEYS "rate_limit:*"
KEYS "token_bucket:*"
KEYS "chat_completion:*"

# 查看键的类型和值
TYPE llm_gateway:rate_limit:requests:some-api-key:12345
GET llm_gateway:rate_limit:requests:some-api-key:12345

# 查看哈希表内容
HGETALL llm_gateway:token_bucket:tokens:some-api-key

# 查看键的过期时间
TTL llm_gateway:chat_completion:abc123

# 删除特定键
DEL llm_gateway:rate_limit:requests:some-api-key:12345

# 删除匹配模式的所有键
EVAL "return redis.call('del', unpack(redis.call('keys', ARGV[1])))" 0 "llm_gateway:rate_limit:*"

# 查看内存使用情况
INFO memory

# 查看连接信息
INFO clients

# 查看统计信息
INFO stats

# 监控实时命令
MONITOR

# 清空所有数据 (谨慎使用!)
FLUSHALL
```

### Redis性能监控

```bash
# 查看慢查询日志
SLOWLOG GET 10

# 重置慢查询日志
SLOWLOG RESET

# 查看配置
CONFIG GET "*"

# 设置配置
CONFIG SET maxmemory 256mb
CONFIG SET maxmemory-policy allkeys-lru

# 保存配置到磁盘
CONFIG REWRITE
```

## 🔍 服务健康检查

### 应用健康检查

```bash
# 基础健康检查
curl -f http://localhost:3000/v1/health

# 详细健康检查
curl -s http://localhost:3000/v1/health/detailed | jq

# 检查特定提供商状态
curl -s http://localhost:3000/v1/health/detailed | jq '.services.providers'
```

### Docker服务状态

```bash
# 查看所有服务状态
docker-compose ps

# 查看服务健康状态
docker-compose ps --format "table {{.Name}}\t{{.Status}}\t{{.Ports}}"

# 检查容器资源使用
docker stats

# 查看容器详细信息
docker inspect llm-gateway
docker inspect llm-gateway-postgres
docker inspect llm-gateway-redis
```

### 系统资源监控

```bash
# 查看系统负载
uptime

# 查看内存使用
free -h

# 查看磁盘使用
df -h

# 查看网络连接
netstat -tlnp | grep -E ':(3000|5432|6379)'

# 查看进程状态
ps aux | grep -E '(node|postgres|redis)'
```

## 📊 监控指标

### Prometheus指标

```bash
# 获取所有指标
curl -s http://localhost:3000/metrics

# 获取特定指标
curl -s http://localhost:3000/metrics | grep llm_gateway_http_requests_total

# 获取指标摘要
curl -s http://localhost:3000/metrics/summary | jq
```

### 关键指标监控

```bash
# HTTP请求总数
curl -s http://localhost:3000/metrics | grep "llm_gateway_http_requests_total"

# LLM请求延迟
curl -s http://localhost:3000/metrics | grep "llm_gateway_llm_request_duration"

# Token使用量
curl -s http://localhost:3000/metrics | grep "llm_gateway_llm_tokens_total"

# 缓存命中率
curl -s http://localhost:3000/metrics | grep "llm_gateway_cache_hit_rate"

# 活跃连接数
curl -s http://localhost:3000/metrics | grep "llm_gateway_active_connections"
```

## 🚨 故障排除

### 常见问题诊断

**1. 服务无法启动**
```bash
# 查看详细日志
docker-compose logs gateway

# 检查端口占用
lsof -i :3000
netstat -tlnp | grep 3000

# 检查环境变量
docker-compose exec gateway env | grep -E "(DB_|REDIS_|API_KEY)"
```

**2. 数据库连接问题**
```bash
# 测试数据库连接
docker-compose exec gateway node -e "
const { Client } = require('pg');
const client = new Client({
  host: 'postgres',
  port: 5432,
  user: 'postgres',
  password: 'password',
  database: 'llm_gateway'
});
client.connect().then(() => console.log('Connected')).catch(console.error);
"

# 检查数据库日志
docker-compose logs postgres

# 重启数据库服务
docker-compose restart postgres
```

**3. Redis连接问题**
```bash
# 测试Redis连接
docker-compose exec gateway node -e "
const Redis = require('ioredis');
const redis = new Redis({ host: 'redis', port: 6379 });
redis.ping().then(console.log).catch(console.error);
"

# 检查Redis日志
docker-compose logs redis

# 重启Redis服务
docker-compose restart redis
```

**4. API调用失败**
```bash
# 测试API密钥
curl -X POST http://localhost:3000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your-api-key" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [{"role": "user", "content": "Hello"}],
    "max_tokens": 10
  }'

# 检查API密钥状态
docker-compose exec postgres psql -U postgres -d llm_gateway -c "
SELECT id, name, enabled, expires_at, last_used_at 
FROM api_keys 
WHERE key = 'your-api-key';
"
```

### 性能问题排查

**1. 高延迟问题**
```bash
# 查看慢查询
docker-compose exec postgres psql -U postgres -d llm_gateway -c "
SELECT query, calls, total_time, mean_time 
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 5;
"

# 查看Redis慢查询
docker-compose exec redis redis-cli SLOWLOG GET 10

# 检查系统负载
docker stats --no-stream
```

**2. 内存使用过高**
```bash
# 查看Node.js内存使用
docker-compose exec gateway node -e "console.log(process.memoryUsage())"

# 查看Redis内存使用
docker-compose exec redis redis-cli INFO memory

# 查看PostgreSQL内存使用
docker-compose exec postgres psql -U postgres -c "
SELECT 
    setting as max_connections,
    (SELECT count(*) FROM pg_stat_activity) as current_connections
FROM pg_settings 
WHERE name = 'max_connections';
"
```

### 日志分析

```bash
# 查看错误日志
docker-compose logs gateway | grep -i error

# 查看特定时间段的日志
docker-compose logs --since="2024-01-01T10:00:00" --until="2024-01-01T11:00:00" gateway

# 实时监控日志
docker-compose logs -f gateway | grep -E "(ERROR|WARN)"

# 统计错误类型
docker-compose logs gateway | grep ERROR | awk '{print $NF}' | sort | uniq -c | sort -nr
```

## 🔧 维护任务

### 定期维护

**每日任务:**
```bash
#!/bin/bash
# daily_maintenance.sh

echo "=== 每日维护任务 $(date) ==="

# 检查服务状态
echo "检查服务状态..."
docker-compose ps

# 检查磁盘空间
echo "检查磁盘空间..."
df -h

# 清理Docker日志
echo "清理Docker日志..."
docker system prune -f

# 备份数据库
echo "备份数据库..."
docker-compose exec postgres pg_dump -U postgres llm_gateway > "backup_$(date +%Y%m%d).sql"

echo "每日维护任务完成"
```

**每周任务:**
```bash
#!/bin/bash
# weekly_maintenance.sh

echo "=== 每周维护任务 $(date) ==="

# 清理过期数据
echo "清理过期数据..."
docker-compose exec postgres psql -U postgres -d llm_gateway -c "SELECT cleanup_expired_data();"

# 重建索引
echo "重建索引..."
docker-compose exec postgres psql -U postgres -d llm_gateway -c "REINDEX DATABASE llm_gateway;"

# 更新统计信息
echo "更新统计信息..."
docker-compose exec postgres psql -U postgres -d llm_gateway -c "ANALYZE;"

# 清理Redis过期键
echo "清理Redis..."
docker-compose exec redis redis-cli --scan --pattern "*" | xargs -L 1000 docker-compose exec redis redis-cli DEL

echo "每周维护任务完成"
```

### 扩容操作

**水平扩容:**
```bash
# 扩展网关实例
docker-compose up -d --scale gateway=3

# 使用负载均衡器 (nginx配置示例)
upstream llm_gateway {
    server localhost:3000;
    server localhost:3001;
    server localhost:3002;
}

server {
    listen 80;
    location / {
        proxy_pass http://llm_gateway;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

**垂直扩容:**
```yaml
# docker-compose.override.yml
version: '3.8'
services:
  gateway:
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G
  
  postgres:
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 1G
```

### 安全检查

```bash
# 检查开放端口
nmap -sT localhost

# 检查SSL证书 (如果使用HTTPS)
openssl s_client -connect localhost:443 -servername your-domain.com

# 检查API密钥安全性
docker-compose exec postgres psql -U postgres -d llm_gateway -c "
SELECT 
    COUNT(*) as total_keys,
    COUNT(CASE WHEN expires_at IS NULL THEN 1 END) as never_expire,
    COUNT(CASE WHEN last_used_at < NOW() - INTERVAL '30 days' THEN 1 END) as unused_30d
FROM api_keys 
WHERE enabled = true;
"

# 检查异常访问
docker-compose exec postgres psql -U postgres -d llm_gateway -c "
SELECT 
    client_ip,
    COUNT(*) as request_count,
    COUNT(CASE WHEN status = 'error' THEN 1 END) as error_count
FROM usage_logs 
WHERE created_at > NOW() - INTERVAL '24 hours'
GROUP BY client_ip 
HAVING COUNT(*) > 1000 OR COUNT(CASE WHEN status = 'error' THEN 1 END) > 100
ORDER BY request_count DESC;
"
```

## 📈 性能优化

### 数据库优化

```sql
-- 创建分区表（按月分区usage_logs）
CREATE TABLE usage_logs_y2024m01 PARTITION OF usage_logs
FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');

-- 创建部分索引
CREATE INDEX CONCURRENTLY idx_usage_logs_recent 
ON usage_logs (created_at, api_key_id) 
WHERE created_at > NOW() - INTERVAL '7 days';

-- 优化查询计划
SET work_mem = '256MB';
SET effective_cache_size = '4GB';
SET shared_buffers = '1GB';
```

### Redis优化

```bash
# 设置内存策略
docker-compose exec redis redis-cli CONFIG SET maxmemory-policy allkeys-lru

# 启用键过期事件
docker-compose exec redis redis-cli CONFIG SET notify-keyspace-events Ex

# 优化持久化
docker-compose exec redis redis-cli CONFIG SET save "900 1 300 10 60 10000"
```

### 应用优化

```bash
# 调整Node.js内存限制
docker-compose exec gateway node --max-old-space-size=2048 dist/main

# 启用集群模式
PM2_INSTANCES=4 docker-compose up -d gateway
```

## 🔄 灾难恢复

### 备份策略

```bash
#!/bin/bash
# backup_strategy.sh

# 全量备份
pg_dump -h localhost -U postgres llm_gateway | gzip > "full_backup_$(date +%Y%m%d_%H%M%S).sql.gz"

# 增量备份（WAL归档）
pg_basebackup -h localhost -U postgres -D backup_base -Ft -z -P

# Redis备份
docker-compose exec redis redis-cli BGSAVE
cp /var/lib/redis/dump.rdb "redis_backup_$(date +%Y%m%d_%H%M%S).rdb"
```

### 恢复流程

```bash
#!/bin/bash
# disaster_recovery.sh

echo "开始灾难恢复流程..."

# 1. 停止所有服务
docker-compose down

# 2. 恢复数据库
gunzip -c full_backup_20240101_120000.sql.gz | docker-compose exec -T postgres psql -U postgres llm_gateway

# 3. 恢复Redis
docker-compose exec redis redis-cli FLUSHALL
docker cp redis_backup_20240101_120000.rdb llm-gateway-redis:/data/dump.rdb

# 4. 重启服务
docker-compose up -d

# 5. 验证恢复
curl -f http://localhost:3000/v1/health

echo "灾难恢复完成"
```

## 📋 运维检查清单

### 日常检查 (每日)
- [ ] 检查所有服务状态
- [ ] 查看错误日志
- [ ] 监控磁盘空间使用
- [ ] 检查API响应时间
- [ ] 验证备份完成

### 周度检查 (每周)
- [ ] 清理过期数据
- [ ] 重建数据库索引
- [ ] 检查安全日志
- [ ] 更新依赖包
- [ ] 性能基准测试

### 月度检查 (每月)
- [ ] 容量规划评估
- [ ] 安全漏洞扫描
- [ ] 灾难恢复演练
- [ ] 文档更新
- [ ] 成本分析

## 🚨 告警配置

### Prometheus告警规则

```yaml
# alerts.yml
groups:
  - name: llm-gateway
    rules:
      - alert: HighErrorRate
        expr: rate(llm_gateway_http_requests_total{status_code=~"5.."}[5m]) > 0.1
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "LLM网关错误率过高"
          
      - alert: HighLatency
        expr: histogram_quantile(0.95, rate(llm_gateway_http_request_duration_seconds_bucket[5m])) > 5
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "LLM网关延迟过高"
          
      - alert: DatabaseConnectionHigh
        expr: pg_stat_activity_count > 80
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "数据库连接数过高"
```

### 监控脚本

```bash
#!/bin/bash
# monitoring_script.sh

# 检查服务可用性
check_service() {
    local service=$1
    local url=$2
    
    if curl -f -s "$url" > /dev/null; then
        echo "✅ $service 正常"
    else
        echo "❌ $service 异常"
        # 发送告警通知
        curl -X POST "https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK" \
             -H 'Content-type: application/json' \
             --data "{\"text\":\"🚨 $service 服务异常\"}"
    fi
}

check_service "LLM网关" "http://localhost:3000/v1/health"
check_service "Prometheus指标" "http://localhost:3000/metrics"
```

## 📞 紧急联系信息

### 关键人员
- **系统管理员**: <EMAIL>
- **开发负责人**: <EMAIL>  
- **运维负责人**: <EMAIL>

### 外部服务商
- **OpenAI支持**: https://help.openai.com
- **Anthropic支持**: https://support.anthropic.com
- **云服务商**: 根据实际使用的云平台

### 紧急处理流程
1. 立即评估影响范围
2. 通知相关人员
3. 执行应急预案
4. 记录处理过程
5. 事后分析总结

---

**注意**: 本文档应定期更新，确保所有操作步骤和联系信息的准确性。在生产环境中执行任何操作前，请务必在测试环境中验证。
