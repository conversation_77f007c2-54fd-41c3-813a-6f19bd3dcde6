version: '3.8'

services:
  # LLM API网关服务
  gateway:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: llm-gateway
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - API_PREFIX=v1
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USERNAME=postgres
      - DB_PASSWORD=password
      - DB_DATABASE=llm_gateway
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - JWT_SECRET=your-super-secret-jwt-key-here
      - OPENAI_API_KEY=${OPENAI_API_KEY:-}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY:-}
      - OLLAMA_BASE_URL=${OLLAMA_BASE_URL:-http://host.docker.internal:11434}
      - CUSTOM_ENDPOINT_URL=${CUSTOM_ENDPOINT_URL:-}
      - CUSTOM_ENDPOINT_API_KEY=${CUSTOM_ENDPOINT_API_KEY:-}
      - CACHE_TTL=3600
      - ENABLE_CACHE=true
      - RATE_LIMIT_WINDOW_MS=60000
      - RATE_LIMIT_MAX_REQUESTS=100
      - ENABLE_METRICS=true
      - LOG_LEVEL=info
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "./healthcheck.sh"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped
    networks:
      - llm-gateway-network

  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: llm-gateway-postgres
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
      - POSTGRES_DB=llm_gateway
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d llm_gateway"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s
    restart: unless-stopped
    networks:
      - llm-gateway-network

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: llm-gateway-redis
    command: redis-server --appendonly yes --requirepass ""
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
      start_period: 10s
    restart: unless-stopped
    networks:
      - llm-gateway-network

  # pgAdmin4 (可选，用于数据库管理)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: llm-gateway-pgadmin
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=admin123
      - PGADMIN_CONFIG_SERVER_MODE=False
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    ports:
      - "8080:80"
    depends_on:
      - postgres
    restart: unless-stopped
    networks:
      - llm-gateway-network
    profiles:
      - tools

  # Redis Commander (可选，用于Redis管理)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: llm-gateway-redis-commander
    environment:
      - REDIS_HOSTS=local:redis:6379
    ports:
      - "8081:8081"
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - llm-gateway-network
    profiles:
      - tools

# 数据卷
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  pgadmin_data:
    driver: local

# 网络
networks:
  llm-gateway-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16