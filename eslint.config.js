// eslint.config.js

// 1. 导入所需的所有模块
import globals from 'globals'; // 用于定义全局变量，替代旧的 env
import tseslint from 'typescript-eslint'; // TypeScript ESLint 的核心包
import eslintConfigPrettier from 'eslint-config-prettier'; // 用来关闭和 Prettier 冲突的规则

// (可选) 如果您坚持使用 eslint-plugin-prettier，请取消下一行的注释
// import pluginPrettier from 'eslint-plugin-prettier';

// 2. 导出配置数组
export default tseslint.config(
  // 全局忽略配置：告诉 ESLint 不要检查这些文件/文件夹
  {
    ignores: [
      '.eslintrc.js', // 删除旧的配置文件后，可以移除此行
      'dist/', // 通常是编译产物
      'node_modules/',
    ],
  },

  // 基础的 TypeScript 配置，我们使用 `recommendedTypeChecked`
  // 因为您的旧配置中启用了 `project`，这能提供更强大的类型检查规则
  ...tseslint.configs.recommendedTypeChecked,

  // 专门为 Jest 测试文件准备的配置
  {
    files: ['**/*.spec.ts', '**/*.test.ts'], // 只对测试文件生效
    ...tseslint.configs.jest,
    languageOptions: {
      globals: {
        ...globals.jest, // 替换旧配置中的 `env: { jest: true }`
      },
    },
  },

  // 适用于所有 TypeScript/JavaScript 文件的全局规则和设置
  {
    languageOptions: {
      parserOptions: {
        project: true,
        tsconfigRootDir: import.meta.dirname,
      },
      globals: {
        ...globals.node,
      },
    },
    // === 在这里修改规则 ===
    rules: {
      // 保持你原有的其他自定义规则
      '@typescript-eslint/interface-name-prefix': 'off',
      '@typescript-eslint/explicit-function-return-type': 'off',
      '@typescript-eslint/explicit-module-boundary-types': 'off',
      '@typescript-eslint/no-unused-vars': [
        'error',
        {
          argsIgnorePattern: '^_', // 忽略以下划线开头的函数参数
          varsIgnorePattern: '^_', // 新增：也忽略以下划线开头的局部变量
        },
      ],

      // === 添加以下规则以完全禁用 'any' 的所有检查 ===
      '@typescript-eslint/no-explicit-any': 'off',
      '@typescript-eslint/no-unsafe-assignment': 'off',
      '@typescript-eslint/no-unsafe-member-access': 'off',
      '@typescript-eslint/no-unsafe-call': 'off',
      '@typescript-eslint/no-unsafe-argument': 'off',
      '@typescript-eslint/no-unsafe-return': 'off',
    },
  },

  // Prettier 的配置必须放在最后！
  // 它的作用是关闭所有可能与 Prettier 格式化冲突的 ESLint 规则。
  eslintConfigPrettier,

  // =========================================================================
  // **备选方案：如果您非常想保留旧的工作流（不推荐）**
  // 如果您希望继续通过 ESLint 运行 Prettier，请：
  // 1. 注释掉上面的 `eslintConfigPrettier`
  // 2. 取消下面这个配置块的注释
  // 3. 确保您已安装 `eslint-plugin-prettier`
  /*
  {
    plugins: { prettier: pluginPrettier },
    rules: {
      'prettier/prettier': 'error', // 把 Prettier 的问题作为 ESLint 错误报出来
    },
  }
  */
);
