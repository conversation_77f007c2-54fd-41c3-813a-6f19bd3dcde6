#!/usr/bin/env node

/**
 * Provider Connection Diagnostic Script
 * 
 * This script helps diagnose and fix common provider connection issues:
 * 1. OpenAI timeouts
 * 2. Anthropic authentication errors
 * 3. Ollama connection issues
 * 4. Custom provider DNS resolution
 */

const https = require('https');
const http = require('http');
const dns = require('dns').promises;

// Configuration
const CONFIG = {
  openai: {
    apiKey: process.env.OPENAI_API_KEY,
    baseUrl: process.env.OPENAI_BASE_URL || 'https://api.openai.com',
    timeout: parseInt(process.env.OPENAI_TIMEOUT || '120000', 10)
  },
  anthropic: {
    apiKey: process.env.ANTHROPIC_API_KEY,
    baseUrl: process.env.ANTHROPIC_BASE_URL || 'https://api.anthropic.com',
    timeout: parseInt(process.env.ANTHROPIC_TIMEOUT || '120000', 10)
  },
  ollama: {
    baseUrl: process.env.OLLAMA_BASE_URL || 'http://localhost:11434',
    timeout: parseInt(process.env.OLLAMA_TIMEOUT || '120000', 10)
  },
  custom: {
    baseUrl: process.env.CUSTOM_ENDPOINT_URL,
    apiKey: process.env.CUSTOM_ENDPOINT_API_KEY,
    timeout: parseInt(process.env.CUSTOM_TIMEOUT || '60000', 10)
  }
};

// Colors for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

async function checkOpenAI() {
  log('\n🔍 Checking OpenAI Provider...', colors.blue);
  
  if (!CONFIG.openai.apiKey || CONFIG.openai.apiKey.includes('your-openai-api-key')) {
    log('❌ Invalid API Key: Please set a valid OPENAI_API_KEY in .env', colors.red);
    return false;
  }

  try {
    const url = new URL('/v1/models', CONFIG.openai.baseUrl);
    const options = {
      hostname: url.hostname,
      port: url.port || 443,
      path: url.pathname,
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${CONFIG.openai.apiKey}`,
        'Content-Type': 'application/json'
      },
      timeout: 5000
    };

    const response = await new Promise((resolve, reject) => {
      const req = https.request(options, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => resolve({ status: res.statusCode, data }));
      });
      
      req.on('error', reject);
      req.on('timeout', () => reject(new Error('Timeout')));
      req.setTimeout(CONFIG.openai.timeout);
      req.end();
    });

    if (response.status === 200) {
      log('✅ OpenAI connection successful', colors.green);
      return true;
    } else {
      log(`❌ OpenAI error: ${response.status}`, colors.red);
      return false;
    }
  } catch (error) {
    log(`❌ OpenAI connection failed: ${error.message}`, colors.red);
    return false;
  }
}

async function checkAnthropic() {
  log('\n🔍 Checking Anthropic Provider...', colors.blue);
  
  if (!CONFIG.anthropic.apiKey || CONFIG.anthropic.apiKey.includes('your-anthropic-api-key')) {
    log('❌ Invalid API Key: Please set a valid ANTHROPIC_API_KEY in .env', colors.red);
    return false;
  }

  try {
    const url = new URL('/v1/messages', CONFIG.anthropic.baseUrl);
    const postData = JSON.stringify({
      model: 'claude-3-haiku-20240307',
      max_tokens: 1,
      messages: [{ role: 'user', content: 'Hi' }]
    });

    const options = {
      hostname: url.hostname,
      port: url.port || 443,
      path: url.pathname,
      method: 'POST',
      headers: {
        'x-api-key': CONFIG.anthropic.apiKey,
        'Content-Type': 'application/json',
        'anthropic-version': '2023-06-01',
        'Content-Length': Buffer.byteLength(postData)
      },
      timeout: 5000
    };

    const response = await new Promise((resolve, reject) => {
      const req = https.request(options, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => resolve({ status: res.statusCode, data }));
      });
      
      req.on('error', reject);
      req.on('timeout', () => reject(new Error('Timeout')));
      req.setTimeout(CONFIG.anthropic.timeout);
      req.write(postData);
      req.end();
    });

    if (response.status === 200 || response.status === 400) {
      log('✅ Anthropic connection successful', colors.green);
      return true;
    } else {
      log(`❌ Anthropic error: ${response.status}`, colors.red);
      return false;
    }
  } catch (error) {
    log(`❌ Anthropic connection failed: ${error.message}`, colors.red);
    return false;
  }
}

async function checkOllama() {
  log('\n🔍 Checking Ollama Provider...', colors.blue);
  
  try {
    const url = new URL('/api/tags', CONFIG.ollama.baseUrl);
    const isHttps = url.protocol === 'https:';
    const client = isHttps ? https : http;

    const options = {
      hostname: url.hostname,
      port: url.port || (isHttps ? 443 : 80),
      path: url.pathname,
      method: 'GET',
      timeout: 5000
    };

    const response = await new Promise((resolve, reject) => {
      const req = client.request(options, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => resolve({ status: res.statusCode, data }));
      });
      
      req.on('error', reject);
      req.on('timeout', () => reject(new Error('Timeout')));
      req.setTimeout(CONFIG.ollama.timeout);
      req.end();
    });

    if (response.status === 200) {
      log('✅ Ollama connection successful', colors.green);
      return true;
    } else {
      log(`❌ Ollama error: ${response.status}`, colors.red);
      return false;
    }
  } catch (error) {
    log(`❌ Ollama connection failed: ${error.message}`, colors.red);
    log('💡 Tip: Make sure Ollama is running with: ollama serve', colors.yellow);
    return false;
  }
}

async function checkCustomProvider() {
  log('\n🔍 Checking Custom Provider...', colors.blue);
  
  if (!CONFIG.custom.baseUrl || CONFIG.custom.baseUrl.includes('your-custom-llm-endpoint')) {
    log('⚠️  Custom provider not configured', colors.yellow);
    return true; // Not an error if not configured
  }

  try {
    const url = new URL(CONFIG.custom.baseUrl);
    const isHttps = url.protocol === 'https:';
    const client = isHttps ? https
