#!/bin/bash

# Provider Connection Test Script
# Usage: ./scripts/test-providers.sh

set -e

echo "🚀 LLM API Gateway Provider Connection Test"
echo "=========================================="

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Check if .env exists
if [ ! -f .env ]; then
    echo -e "${RED}❌ .env file not found${NC}"
    exit 1
fi

# Load environment variables
source .env

# Function to test provider
test_provider() {
    local provider=$1
    local api_key=$2
    local endpoint=$3
    
    echo -e "\n🔍 Testing $provider..."
    
    if [[ -z "$api_key" ]] || [[ "$api_key" == *"your-"* ]]; then
        echo -e "${YELLOW}⚠️  $provider API key not configured${NC}"
        return 1
    fi
    
    echo -e "${GREEN}✅ $provider API key configured${NC}"
    return 0
}

# Test OpenAI
test_provider "OpenAI" "$OPENAI_API_KEY" "$OPENAI_BASE_URL"

# Test Anthropic
test_provider "Anthropic" "$ANTHROPIC_API_KEY" "$ANTHROPIC_BASE_URL"

# Test Ollama
echo -e "\n🔍 Testing Ollama..."
if curl -s "$OLLAMA_BASE_URL/api/tags" > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Ollama is running${NC}"
else
    echo -e "${RED}❌ Ollama is not running or not accessible${NC}"
    echo -e "${YELLOW}💡 Start Ollama with: ollama serve${NC}"
fi

# Test Custom Provider
if [[ -n "$CUSTOM_ENDPOINT_URL" ]] && [[ "$CUSTOM_ENDPOINT_URL" != *"your-custom"* ]]; then
    test_provider "Custom" "$CUSTOM_ENDPOINT_API_KEY" "$CUSTOM_ENDPOINT_URL"
else
    echo -e "\n${YELLOW}⚠️  Custom provider not configured${NC}"
fi

echo -e "\n🎯 Summary"
echo "========="
echo "Run the diagnostic script for detailed testing:"
echo "node scripts/diagnose-providers.js"
echo ""
echo "Or test the API directly:"
echo "curl -X GET http://localhost:3000/health/detailed"