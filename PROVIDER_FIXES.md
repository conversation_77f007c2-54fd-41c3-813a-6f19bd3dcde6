# LLM API Gateway Provider Connection Fixes

## 🔍 Issue Analysis Summary

Based on the diagnostic analysis, here are the specific fixes for each provider issue:

### 1. **OpenAI Provider - Connection Timeouts**
**Root Causes:**
- Invalid placeholder API keys in .env file
- Default timeout of 60s may be insufficient for complex requests
- Network connectivity issues

**Fixes Applied:**
- ✅ Updated .env with proper timeout configuration
- ✅ Added configurable timeout via environment variables
- ✅ Enhanced error handling in provider implementation

### 2. **Anthropic Provider - Authentication Error**
**Root Causes:**
- Invalid placeholder API keys in .env file
- Missing proper authentication headers
- API key format issues

**Fixes Applied:**
- ✅ Updated .env with proper API key format
- ✅ Added configurable timeout via environment variables
- ✅ Enhanced authentication header configuration

### 3. **Ollama Provider - Connection Issues**
**Root Causes:**
- Ollama service not running on localhost:11434
- Network connectivity issues
- Service startup problems

**Fixes Applied:**
- ✅ Added diagnostic script to check Ollama status
- ✅ Added configurable timeout via environment variables
- ✅ Enhanced error messages with helpful tips

### 4. **Custom Provider - DNS Resolution Failure**
**Root Causes:**
- Invalid placeholder URL in .env file
- DNS resolution issues
- Network connectivity problems

**Fixes Applied:**
- ✅ Commented out placeholder configuration
- ✅ Added DNS resolution checks
- ✅ Added configurable timeout via environment variables

## 🛠️ Configuration Updates

### Updated .env Configuration
```bash
# OpenAI Configuration
OPENAI_API_KEY=sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_TIMEOUT=120000

# Anthropic Configuration
ANTHROPIC_API_KEY=sk-ant-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
ANTHROPIC_BASE_URL=https://api.anthropic.com
ANTHROPIC_TIMEOUT=120000

# Ollama Configuration
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_TIMEOUT=120000

# Custom Endpoint Configuration
# CUSTOM_ENDPOINT_URL=https://your-actual-endpoint.com
# CUSTOM_ENDPOINT_API_KEY=your-actual-api-key
CUSTOM_TIMEOUT=120000
```

### Updated providers.config.ts
- Added configurable timeouts via environment variables
- Enhanced error handling
- Added proper type parsing for timeout values

## 🚀 Quick Start Guide

### 1. Set Valid API Keys
Replace the placeholder values in .env with actual API keys:
```bash
# Get your OpenAI API key from: https://platform.openai.com/api-keys
OPENAI_API_KEY=sk-your-actual-openai-key

# Get your Anthropic API key from: https://console.anthropic.com/
ANTHROPIC_API_KEY=sk-ant-your-actual-anthropic-key
```

### 2. Start Ollama Service
```bash
# Install Ollama if not already installed
curl -fsSL https://ollama.ai/install.sh | sh

# Start Ollama service
ollama serve

# Pull some models
ollama pull llama2
ollama pull mistral
```

### 3. Run Diagnostic Script
```bash
# Make script executable
chmod +x scripts/diagnose-providers.js

# Run diagnostics
node scripts/diagnose-providers.js
```

### 4. Test Provider Connections
```bash
# Test all providers
curl http://localhost:3000/health/detailed

# Test specific provider
curl -X POST http://localhost:3000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [{"role": "user", "content": "Hello!"}]
  }'
```

## 🔧 Troubleshooting Guide

### OpenAI Issues
1. **Timeout Errors**: Increase `OPENAI_TIMEOUT` in .env
2. **Authentication Errors**: Verify API key format and validity
3. **Rate Limits**: Check OpenAI dashboard for usage limits

### Anthropic Issues
1. **Invalid Authentication**: Ensure API key starts with `sk-ant-`
2. **Model Not Found**: Verify model names in providers.config.ts
3. **Rate Limits**: Check Anthropic console for usage

### Ollama Issues
1. **Connection Refused**: Ensure Ollama is running with `ollama serve`
2. **Model Not Found**: Pull required models with `ollama pull <model-name>`
3. **Port Issues**: Check if port 11434 is available

### Custom Provider Issues
1. **DNS Resolution**: Check DNS settings and network connectivity
2. **SSL Certificate**: Verify SSL certificate validity for HTTPS endpoints
3. **CORS Issues**: Configure CORS headers on custom endpoint

## 📊 Monitoring and Health Checks

### Health Check Endpoints
- Basic health: `GET /health`
- Detailed health: `GET /health/detailed`
- Provider status: `GET /v1/models`

### Log Monitoring
Check application logs for detailed error messages:
```bash
# View logs
docker logs llm-gateway
# or
pm2 logs llm-gateway
```

## 🔄 Next Steps

1. **Replace placeholder API keys** with actual values
2. **Start required services** (Ollama, etc.)
3. **Run diagnostic script** to verify connections
4. **Test each provider** individually
5. **Monitor logs** for any remaining issues

## 📞 Support

If issues persist after applying these fixes:
1. Check the diagnostic script output
2. Review application logs
3. Verify network connectivity
4. Check provider-specific documentation