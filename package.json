{"name": "llm-api-gateway", "version": "1.0.0", "description": "高性能多模型LLM API网关", "author": "CodeBuddy", "private": true, "packageManager": "pnpm@9.14.4", "license": "MIT", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "NODE_OPTIONS='--no-warnings --experimental-specifier-resolution=node' nest start", "start:dev": "NODE_OPTIONS='--no-warnings --experimental-specifier-resolution=node' nest start --watch", "start:debug": "NODE_OPTIONS='--no-warnings --experimental-specifier-resolution=node' nest start --debug --watch", "start:prod": "NODE_OPTIONS=--no-warnings node dist/main", "lint": "eslint .", "lint:fix": "eslint . --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "typeorm": "NODE_OPTIONS=--no-warnings ts-node -P tsconfig.typeorm.json -r tsconfig-paths/register ./node_modules/typeorm/cli.js", "migration:generate": "npm run typeorm -- migration:generate -d src/config/database.config.ts", "migration:run": "npm run typeorm -- migration:run -d src/config/database.config.ts", "migration:revert": "npm run typeorm -- migration:revert -d src/config/database.config.ts"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/config": "^3.1.1", "@nestjs/core": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/swagger": "^7.1.17", "@nestjs/terminus": "^10.2.0", "@nestjs/throttler": "^5.0.1", "@nestjs/typeorm": "^10.0.1", "axios": "^1.6.2", "bcrypt": "^5.1.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "ioredis": "^5.3.2", "jsonwebtoken": "^9.0.2", "pg": "^8.11.3", "prom-client": "^15.0.0", "redis": "^4.6.10", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "typeorm": "^0.3.17", "uuid": "^9.0.1"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/bcrypt": "^5.0.2", "@types/compression": "^1.7.5", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20.3.1", "@types/pg": "^8.10.9", "@types/supertest": "^2.0.12", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^8.39.0", "@typescript-eslint/parser": "^8.39.0", "eslint": "^9.32.0", "eslint-config-prettier": "^10.1.8", "globals": "^16.3.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^7.1.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3", "typescript-eslint": "^8.39.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "engines": {"node": ">=20.0.0", "pnpm": ">=8.0.0"}, "volta": {"node": "23.3.0", "pnpm": "9.14.4"}}