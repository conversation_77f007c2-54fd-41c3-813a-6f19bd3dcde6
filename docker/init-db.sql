-- LLM API网关数据库初始化脚本
-- 创建必要的扩展、表结构和初始数据

-- 注意：在执行此脚本时，可能会看到以下警告：
-- ExperimentalWarning: CommonJS module ... is loading ES Module ... using require().
-- 这是由于 TypeORM 使用 CommonJS 加载 ES 模块导致的，不影响功能。
-- 如需消除警告，可以在 tsconfig.json 中设置 "module": "CommonJS"
-- 或者在运行命令时添加 NODE_OPTIONS=--no-warnings

-- 启用UUID扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 启用INET扩展（用于IP地址存储）
CREATE EXTENSION IF NOT EXISTS "cidr";

-- 设置时区
SET timezone = 'UTC';

-- 创建枚举类型
DO $$ BEGIN
    CREATE TYPE usage_status AS ENUM ('success', 'error', 'timeout');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(100) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    is_admin BOOLEAN DEFAULT FALSE,
    last_login_at TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- 创建API密钥表
CREATE TABLE IF NOT EXISTS api_keys (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    key VARCHAR(64) NOT NULL UNIQUE,
    name VARCHAR(255) NOT NULL,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    description TEXT,
    enabled BOOLEAN DEFAULT TRUE,
    allowed_models JSONB,
    allowed_ips JSONB,
    rate_limit INTEGER DEFAULT 1000,
    monthly_token_quota INTEGER DEFAULT 100000,
    monthly_token_used INTEGER DEFAULT 0,
    monthly_cost DECIMAL(10, 4) DEFAULT 0,
    monthly_cost_limit DECIMAL(10, 4) DEFAULT 100,
    total_requests INTEGER DEFAULT 0,
    total_tokens INTEGER DEFAULT 0,
    last_used_at TIMESTAMP,
    expires_at TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- 创建使用日志表
CREATE TABLE IF NOT EXISTS usage_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    api_key_id UUID NOT NULL REFERENCES api_keys(id) ON DELETE CASCADE,
    model VARCHAR(100) NOT NULL,
    provider VARCHAR(50) NOT NULL,
    prompt_tokens INTEGER NOT NULL,
    completion_tokens INTEGER NOT NULL,
    total_tokens INTEGER NOT NULL,
    cost DECIMAL(10, 6) NOT NULL,
    response_time INTEGER NOT NULL,
    status VARCHAR(20) NOT NULL,
    error_message TEXT,
    client_ip INET,
    user_agent VARCHAR(500),
    request_id VARCHAR(100),
    created_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- 创建模型配置表
CREATE TABLE IF NOT EXISTS model_configs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    model_name VARCHAR(100) NOT NULL UNIQUE,
    provider VARCHAR(50) NOT NULL,
    prompt_price_per_1k DECIMAL(10, 6) NOT NULL,
    completion_price_per_1k DECIMAL(10, 6) NOT NULL,
    context_window INTEGER NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- 创建提供商配置表
CREATE TABLE IF NOT EXISTS provider_configs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    provider_name VARCHAR(50) NOT NULL UNIQUE,
    base_url VARCHAR(255) NOT NULL,
    api_key VARCHAR(255),
    weight INTEGER DEFAULT 1,
    timeout_ms INTEGER DEFAULT 30000,
    max_retries INTEGER DEFAULT 3,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- 创建索引优化查询性能
CREATE INDEX IF NOT EXISTS idx_api_keys_user_id ON api_keys(user_id);
CREATE INDEX IF NOT EXISTS idx_api_keys_enabled_expires ON api_keys(enabled, expires_at) WHERE enabled = true;

CREATE INDEX IF NOT EXISTS idx_usage_logs_api_key_id_created_at ON usage_logs(api_key_id, created_at);
CREATE INDEX IF NOT EXISTS idx_usage_logs_model_created_at ON usage_logs(model, created_at);
CREATE INDEX IF NOT EXISTS idx_usage_logs_provider_created_at ON usage_logs(provider, created_at);
CREATE INDEX IF NOT EXISTS idx_usage_logs_created_at_status ON usage_logs(created_at, status);

-- 创建示例用户（仅用于开发和测试）
INSERT INTO users (
    id,
    username,
    email,
    password_hash,
    full_name,
    is_active,
    is_admin,
    created_at,
    updated_at
) VALUES (
    uuid_generate_v4(),
    'admin',
    '<EMAIL>',
    '$2b$10$EpRnTzVlqHNP0.fUbXUwSOyuiXe/QLSUG6xNekdHgTGmrpHEfIoxm', -- 密码: password
    '系统管理员',
    true,
    true,
    NOW(),
    NOW()
) ON CONFLICT (username) DO NOTHING;

-- 创建示例API密钥（仅用于开发和测试）
INSERT INTO api_keys (
    id,
    key,
    name,
    user_id,
    description,
    enabled,
    rate_limit,
    monthly_token_quota,
    monthly_cost_limit,
    created_at,
    updated_at
) VALUES (
    uuid_generate_v4(),
    'llm-gw-demo-key-for-testing-only-12345678',
    '演示密钥',
    (SELECT id FROM users WHERE username = 'admin'),
    '用于演示和测试的API密钥，生产环境请删除',
    true,
    1000,
    100000,
    100.00,
    NOW(),
    NOW()
) ON CONFLICT (key) DO NOTHING;

-- 创建示例模型配置
INSERT INTO model_configs (
    model_name,
    provider,
    prompt_price_per_1k,
    completion_price_per_1k,
    context_window,
    is_active
) VALUES
    ('gpt-3.5-turbo', 'openai', 0.0015, 0.002, 16385, true),
    ('gpt-4', 'openai', 0.03, 0.06, 8192, true),
    ('gpt-4-turbo', 'openai', 0.01, 0.03, 128000, true),
    ('claude-3-opus', 'anthropic', 0.015, 0.075, 200000, true),
    ('claude-3-sonnet', 'anthropic', 0.003, 0.015, 200000, true),
    ('llama2', 'ollama', 0.0001, 0.0001, 4096, true)
ON CONFLICT (model_name) DO NOTHING;

-- 创建示例提供商配置
INSERT INTO provider_configs (
    provider_name,
    base_url,
    api_key,
    weight,
    is_active
) VALUES
    ('openai', 'https://api.openai.com/v1', '${OPENAI_API_KEY}', 10, true),
    ('anthropic', 'https://api.anthropic.com/v1', '${ANTHROPIC_API_KEY}', 5, true),
    ('ollama', 'http://localhost:11434', NULL, 1, true)
ON CONFLICT (provider_name) DO NOTHING;

-- 创建数据库函数：清理过期数据
CREATE OR REPLACE FUNCTION cleanup_expired_data()
RETURNS void AS $$
BEGIN
    -- 删除30天前的使用日志
    DELETE FROM usage_logs 
    WHERE created_at < NOW() - INTERVAL '30 days';
    
    -- 禁用已过期的API密钥
    UPDATE api_keys 
    SET enabled = false 
    WHERE expires_at IS NOT NULL 
    AND expires_at < NOW() 
    AND enabled = true;
    
    RAISE NOTICE '已清理过期数据';
END;
$$ LANGUAGE plpgsql;

-- 创建视图：API密钥使用统计
CREATE OR REPLACE VIEW api_key_usage_stats AS
SELECT 
    ak.id,
    ak.name,
    ak.user_id,
    ak.enabled,
    ak.total_requests,
    ak.total_tokens,
    ak.monthly_token_used,
    ak.monthly_cost,
    ak.last_used_at,
    COUNT(ul.id) as recent_requests,
    SUM(ul.total_tokens) as recent_tokens,
    SUM(ul.cost) as recent_cost,
    AVG(ul.response_time) as avg_response_time
FROM api_keys ak
LEFT JOIN usage_logs ul ON ak.id = ul.api_key_id 
    AND ul.created_at > NOW() - INTERVAL '24 hours'
GROUP BY ak.id, ak.name, ak.user_id, ak.enabled, 
         ak.total_requests, ak.total_tokens, 
         ak.monthly_token_used, ak.monthly_cost, ak.last_used_at;

-- 创建视图：模型使用统计
CREATE OR REPLACE VIEW model_usage_stats AS
SELECT 
    model,
    provider,
    COUNT(*) as request_count,
    SUM(total_tokens) as total_tokens,
    SUM(cost) as total_cost,
    AVG(response_time) as avg_response_time,
    COUNT(CASE WHEN status = 'success' THEN 1 END) as success_count,
    COUNT(CASE WHEN status = 'error' THEN 1 END) as error_count,
    DATE_TRUNC('day', created_at) as date
FROM usage_logs
WHERE created_at > NOW() - INTERVAL '30 days'
GROUP BY model, provider, DATE_TRUNC('day', created_at)
ORDER BY date DESC, request_count DESC;

-- 创建视图：用户使用统计
CREATE OR REPLACE VIEW user_usage_stats AS
SELECT 
    u.id as user_id,
    u.username,
    COUNT(DISTINCT ak.id) as api_key_count,
    SUM(ak.total_requests) as total_requests,
    SUM(ak.total_tokens) as total_tokens,
    SUM(ak.monthly_token_used) as monthly_token_used,
    SUM(ak.monthly_cost) as monthly_cost
FROM users u
LEFT JOIN api_keys ak ON u.id = ak.user_id
GROUP BY u.id, u.username;

-- 创建触发器：更新API密钥统计信息
CREATE OR REPLACE FUNCTION update_api_key_stats()
RETURNS TRIGGER AS $$
BEGIN
    -- 更新API密钥的统计信息
    UPDATE api_keys
    SET 
        total_requests = total_requests + 1,
        total_tokens = total_tokens + NEW.total_tokens,
        monthly_token_used = monthly_token_used + NEW.total_tokens,
        monthly_cost = monthly_cost + NEW.cost,
        last_used_at = NOW(),
        updated_at = NOW()
    WHERE id = NEW.api_key_id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trg_update_api_key_stats
AFTER INSERT ON usage_logs
FOR EACH ROW
EXECUTE FUNCTION update_api_key_stats();

-- 创建触发器：每月重置API密钥配额
CREATE OR REPLACE FUNCTION reset_monthly_quotas()
RETURNS void AS $$
BEGIN
    UPDATE api_keys
    SET 
        monthly_token_used = 0,
        monthly_cost = 0,
        updated_at = NOW()
    WHERE enabled = true;
    
    RAISE NOTICE '已重置所有API密钥的月度配额';
END;
$$ LANGUAGE plpgsql;

-- 注意：需要使用外部调度器（如cron）来定期执行此函数
-- 例如：每月1日凌晨执行 SELECT reset_monthly_quotas();

COMMIT;