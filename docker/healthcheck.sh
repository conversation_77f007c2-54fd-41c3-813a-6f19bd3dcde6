#!/bin/sh

# LLM API网关健康检查脚本
# 检查应用是否正常运行

set -e

# 默认配置
HOST=${HOST:-localhost}
PORT=${PORT:-3000}
API_PREFIX=${API_PREFIX:-v1}

# 健康检查URL
HEALTH_URL="http://${HOST}:${PORT}/${API_PREFIX}/health"

# 使用wget检查健康状态
if command -v wget >/dev/null 2>&1; then
    wget --no-verbose --tries=1 --spider "$HEALTH_URL" || exit 1
elif command -v curl >/dev/null 2>&1; then
    curl -f "$HEALTH_URL" >/dev/null 2>&1 || exit 1
else
    echo "错误: 未找到wget或curl命令"
    exit 1
fi

echo "健康检查通过"
exit 0