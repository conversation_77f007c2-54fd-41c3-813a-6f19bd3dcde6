# 高性能多模型LLM API网关

一个基于NestJS构建的高性能、高可用性的多模型LLM API网关，提供统一的API接口来访问多个大语言模型提供商。

## ✨ 特性

- 🚀 **高性能**: 基于NestJS和TypeScript构建，支持并发处理
- 🔄 **多提供商支持**: 支持OpenAI、Anthropic、Ollama和自定义端点
- 🎯 **智能路由**: 支持轮询、加权轮询、最少连接等负载均衡策略
- 🛡️ **安全认证**: 基于API密钥的认证和权限管理
- ⚡ **速率限制**: 基于Redis的令牌桶算法实现精细化速率控制
- 💾 **智能缓存**: Redis缓存相同请求，提高响应速度并降低成本
- 📊 **监控指标**: 集成Prometheus指标，支持Grafana可视化
- 🔄 **流式响应**: 完整支持Server-Sent Events流式输出
- 🏥 **健康检查**: 完善的健康检查和故障转移机制
- 🐳 **容器化**: 完整的Docker和docker-compose支持

## 🛠️ 技术栈

- **语言**: TypeScript 5.x (strict模式)
- **框架**: NestJS 10.x
- **运行环境**: Node.js 20.x LTS
- **包管理器**: pnpm
- **数据库**: PostgreSQL 15
- **缓存**: Redis 7.x
- **监控**: Prometheus + Grafana
- **容器化**: Docker + Docker Compose

## 🚀 快速开始

### 使用Docker (推荐)

1. **克隆项目**
```bash
git clone <repository-url>
cd llm-api-gateway
```

2. **配置环境变量**
```bash
cp .env.example .env
# 编辑.env文件，填入你的API密钥
```

3. **启动服务**
```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f gateway
```

4. **访问服务**
- API网关: http://localhost:3000
- API文档: http://localhost:3000/api
- 健康检查: http://localhost:3000/v1/health
- 监控指标: http://localhost:3000/metrics
- pgAdmin: http://localhost:8080 (可选)
- Redis Commander: http://localhost:8081 (可选)

### 本地开发环境

#### 环境准备

**macOS:**
```bash
# 安装Node.js 20.x
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
nvm install 20
nvm use 20

# 安装pnpm
npm install -g pnpm

# 安装PostgreSQL和Redis (使用Homebrew)
brew install postgresql@15 redis
brew services start postgresql@15
brew services start redis
```

**Linux/WSL2:**
```bash
# 安装Node.js 20.x
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
nvm install 20
nvm use 20

# 安装pnpm
npm install -g pnpm

# 安装PostgreSQL和Redis (Ubuntu/Debian)
sudo apt update
sudo apt install postgresql-15 redis-server
sudo systemctl start postgresql
sudo systemctl start redis-server
```

#### 项目设置

1. **安装依赖**
```bash
pnpm install
```

2. **配置环境变量**
```bash
cp .env.example .env
# 编辑.env文件，配置数据库连接和API密钥
```

3. **数据库设置**
```bash
# 创建数据库
sudo -u postgres createdb llm_gateway

# 运行迁移
pnpm run migration:run
```

4. **启动开发服务器**
```bash
pnpm run start:dev
```

#### VSCode配置

创建`.vscode/settings.json`:
```json
{
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "typescript.preferences.importModuleSpecifier": "relative",
  "files.associations": {
    "*.env*": "dotenv"
  }
}
```

推荐安装的VSCode插件:
- ESLint
- Prettier - Code formatter
- Docker
- PostgreSQL
- Redis

## 📖 API文档

启动应用后，访问 http://localhost:3000/api 查看完整的Swagger API文档。

### 主要接口

#### 聊天完成
```bash
POST /v1/chat/completions
Content-Type: application/json
X-API-Key: your-api-key

{
  "model": "gpt-3.5-turbo",
  "messages": [
    {"role": "user", "content": "你好"}
  ],
  "temperature": 0.7,
  "stream": false
}
```

#### 获取模型列表
```bash
GET /v1/models
X-API-Key: your-api-key
```

#### 健康检查
```bash
GET /v1/health
```

#### 监控指标
```bash
GET /metrics
```

## 🔧 配置说明

### 环境变量

| 变量名 | 描述 | 默认值 |
|--------|------|--------|
| `NODE_ENV` | 运行环境 | `development` |
| `PORT` | 服务端口 | `3000` |
| `DB_HOST` | 数据库主机 | `localhost` |
| `DB_PORT` | 数据库端口 | `5432` |
| `REDIS_HOST` | Redis主机 | `localhost` |
| `REDIS_PORT` | Redis端口 | `6379` |
| `OPENAI_API_KEY` | OpenAI API密钥 | - |
| `ANTHROPIC_API_KEY` | Anthropic API密钥 | - |
| `OLLAMA_BASE_URL` | Ollama服务地址 | `http://localhost:11434` |

### 支持的模型

#### OpenAI
- gpt-4
- gpt-4-turbo
- gpt-3.5-turbo
- gpt-3.5-turbo-16k

#### Anthropic
- claude-3-opus-20240229
- claude-3-sonnet-20240229
- claude-3-haiku-20240307
- claude-2.1

#### Ollama
- llama2
- codellama
- mistral
- neural-chat

## 🔍 监控和运维

### 健康检查

```bash
# 基础健康检查
curl http://localhost:3000/v1/health

# 详细健康检查
curl http://localhost:3000/v1/health/detailed
```

### 监控指标

访问 http://localhost:3000/metrics 获取Prometheus格式的监控指标。

主要指标包括:
- HTTP请求总数和延迟
- LLM请求总数和延迟
- Token使用量统计
- 费用统计
- 缓存命中率
- 活跃连接数
- 速率限制命中次数

### 日志管理

```bash
# 查看应用日志
docker-compose logs -f gateway

# 查看数据库日志
docker-compose logs -f postgres

# 查看Redis日志
docker-compose logs -f redis
```

## 🛠️ 故障排除

### 常见问题

**1. Docker在M1/M2 Mac上构建失败**
```bash
# 使用buildx构建多平台镜像
docker buildx build --platform linux/amd64,linux/arm64 -t llm-gateway .
```

**2. 连接不上PostgreSQL或Redis**
```bash
# 检查容器状态
docker-compose ps

# 检查网络连接
docker-compose exec gateway ping postgres
docker-compose exec gateway ping redis

# 重启服务
docker-compose restart postgres redis
```

**3. API密钥认证失败**
- 确保在请求头中包含 `X-API-Key` 或 `Authorization: Bearer <key>`
- 检查API密钥是否已启用且未过期
- 查看数据库中的api_keys表确认密钥存在

**4. 模型调用失败**
- 检查对应提供商的API密钥是否正确配置
- 确认网络连接正常
- 查看应用日志获取详细错误信息

### 重置数据库

```bash
# 停止服务
docker-compose down

# 删除数据卷
docker volume rm llm-api-gateway_postgres_data

# 重新启动
docker-compose up -d
```

### 性能优化

1. **数据库优化**
   - 定期清理过期的使用日志
   - 优化查询索引
   - 调整连接池大小

2. **Redis优化**
   - 设置合适的内存限制
   - 配置持久化策略
   - 监控内存使用情况

3. **应用优化**
   - 调整并发连接数
   - 优化缓存策略
   - 配置合适的超时时间

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [NestJS](https://nestjs.com/) - 渐进式Node.js框架
- [TypeORM](https://typeorm.io/) - TypeScript ORM
- [Redis](https://redis.io/) - 内存数据结构存储
- [PostgreSQL](https://www.postgresql.org/) - 开源关系型数据库
- [Prometheus](https://prometheus.io/) - 监控和告警工具包

---

如有问题或建议，请提交Issue或联系维护者。