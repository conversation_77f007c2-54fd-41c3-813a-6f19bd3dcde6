# 编译输出
/dist
/node_modules

# 日志
logs
*.log
npm-debug.log*
pnpm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 覆盖率目录
lib-cov
coverage
*.lcov

# nyc 测试覆盖率
.nyc_output

# Grunt 中间和输出目录
.grunt

# Bower 依赖目录
bower_components

# node-waf 配置
.lock-wscript

# 编译的二进制插件
build/Release

# 依赖目录
node_modules/
jspm_packages/

# TypeScript v1 声明文件
typings/

# TypeScript 缓存
*.tsbuildinfo

# 可选的 npm 缓存目录
.npm

# 可选的 eslint 缓存
.eslintcache

# Microbundle 缓存
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# 可选的 REPL 历史
.node_repl_history

# yarn v2 的输出
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# 环境变量
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# parcel-bundler 缓存
.cache
.parcel-cache

# Next.js 构建输出
.next

# Nuxt.js 构建/生成输出
.nuxt
dist

# Gatsby 文件
.cache/
public

# Storybook 构建输出
.out
.storybook-out

# Temporary 文件夹
tmp/
temp/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker
.dockerignore

# 数据库
*.sqlite
*.db
/.codebuddy/
/.idea/
